#!/usr/bin/env python3
"""
Real-world debugging tools for the autotyper to investigate threading, OCR content, and performance issues.

These tools help identify issues that only occur in real-world execution environments.
"""

import threading
import time
import json
import os
from datetime import datetime
from collections import deque
import tempfile

class ThreadSafetyMonitor:
    """Monitor thread safety and detect race conditions."""
    
    def __init__(self):
        self.lock = threading.Lock()
        self.thread_events = deque(maxlen=1000)  # Keep last 1000 events
        self.typing_active_changes = []
        self.capture_active_changes = []
        
    def log_thread_event(self, event_type, thread_name, details=""):
        """Log thread events with timestamps."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        event = {
            'timestamp': timestamp,
            'event_type': event_type,
            'thread_name': thread_name,
            'thread_id': threading.get_ident(),
            'details': details
        }
        
        with self.lock:
            self.thread_events.append(event)
            print(f"[THREAD] [{timestamp}] {thread_name}({threading.get_ident()}): {event_type} - {details}")
    
    def log_typing_active_change(self, old_value, new_value, location, thread_name):
        """Log changes to typing_active with call stack info."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        change = {
            'timestamp': timestamp,
            'old_value': old_value,
            'new_value': new_value,
            'location': location,
            'thread_name': thread_name,
            'thread_id': threading.get_ident()
        }
        
        with self.lock:
            self.typing_active_changes.append(change)
            print(f"[TYPING_ACTIVE] [{timestamp}] {thread_name}: {old_value} → {new_value} at {location}")
    
    def log_capture_active_change(self, old_value, new_value, location, thread_name):
        """Log changes to capture_active."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        change = {
            'timestamp': timestamp,
            'old_value': old_value,
            'new_value': new_value,
            'location': location,
            'thread_name': thread_name,
            'thread_id': threading.get_ident()
        }
        
        with self.lock:
            self.capture_active_changes.append(change)
            print(f"[CAPTURE_ACTIVE] [{timestamp}] {thread_name}: {old_value} → {new_value} at {location}")
    
    def detect_race_conditions(self):
        """Analyze logged events to detect potential race conditions."""
        print("\n" + "="*60)
        print("THREAD SAFETY ANALYSIS")
        print("="*60)
        
        # Check for rapid state changes
        rapid_changes = []
        for i in range(1, len(self.typing_active_changes)):
            prev = self.typing_active_changes[i-1]
            curr = self.typing_active_changes[i]
            
            # Parse timestamps to calculate time difference
            prev_time = datetime.strptime(prev['timestamp'], "%H:%M:%S.%f")
            curr_time = datetime.strptime(curr['timestamp'], "%H:%M:%S.%f")
            time_diff = (curr_time - prev_time).total_seconds()
            
            if time_diff < 0.1:  # Changes within 100ms
                rapid_changes.append((prev, curr, time_diff))
        
        if rapid_changes:
            print(f"⚠️  POTENTIAL RACE CONDITIONS DETECTED: {len(rapid_changes)} rapid state changes")
            for prev, curr, time_diff in rapid_changes:
                print(f"   {prev['timestamp']} → {curr['timestamp']} ({time_diff:.3f}s)")
                print(f"   {prev['thread_name']} → {curr['thread_name']}")
        else:
            print("✅ No obvious race conditions detected")
        
        # Check for cross-thread modifications
        cross_thread_mods = []
        for change in self.typing_active_changes:
            if change['thread_name'] not in ['MainThread', 'typing_thread', 'capture_thread']:
                cross_thread_mods.append(change)
        
        if cross_thread_mods:
            print(f"⚠️  UNEXPECTED THREAD MODIFICATIONS: {len(cross_thread_mods)}")
            for mod in cross_thread_mods:
                print(f"   {mod['timestamp']}: {mod['thread_name']} modified typing_active")
        
        return len(rapid_changes) == 0 and len(cross_thread_mods) == 0

class OCRContentAnalyzer:
    """Analyze OCR content to identify problematic patterns."""
    
    def __init__(self):
        self.ocr_captures = deque(maxlen=100)  # Keep last 100 captures
        self.problematic_patterns = []
        
    def log_ocr_capture(self, raw_text, normalized_text, similarity_to_previous):
        """Log OCR capture with analysis."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        capture = {
            'timestamp': timestamp,
            'raw_text': raw_text,
            'normalized_text': normalized_text,
            'raw_length': len(raw_text),
            'normalized_length': len(normalized_text),
            'similarity_to_previous': similarity_to_previous,
            'line_count': len(normalized_text.split('\n')) if normalized_text else 0
        }
        
        self.ocr_captures.append(capture)
        
        similarity_str = f"{similarity_to_previous:.2f}" if similarity_to_previous is not None else "N/A"
        print(f"[OCR] [{timestamp}] Captured {len(raw_text)} chars → {len(normalized_text)} chars (similarity: {similarity_str})")
        
        # Analyze for problematic patterns
        self.analyze_capture(capture)
    
    def analyze_capture(self, capture):
        """Analyze a single capture for problematic patterns."""
        issues = []
        
        # Check for empty captures
        if not capture['normalized_text']:
            issues.append("EMPTY_NORMALIZED_TEXT")
        
        # Check for excessive length changes
        length_change_ratio = capture['normalized_length'] / max(capture['raw_length'], 1)
        if length_change_ratio < 0.5:
            issues.append(f"EXCESSIVE_NORMALIZATION_REDUCTION_{length_change_ratio:.2f}")
        
        # Check for unusual characters
        unusual_chars = set(capture['raw_text']) - set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 \n\t.,!?;:-()[]{}"\'/\\')
        if unusual_chars:
            issues.append(f"UNUSUAL_CHARACTERS_{len(unusual_chars)}")
        
        # Check for very low similarity with rapid changes
        if (capture['similarity_to_previous'] is not None and
            capture['similarity_to_previous'] < 0.3 and len(self.ocr_captures) > 1):
            issues.append("RAPID_CONTENT_CHANGE")
        
        if issues:
            self.problematic_patterns.append({
                'timestamp': capture['timestamp'],
                'issues': issues,
                'raw_preview': capture['raw_text'][:100],
                'normalized_preview': capture['normalized_text'][:100]
            })
            print(f"   ⚠️  OCR ISSUES: {', '.join(issues)}")
    
    def generate_ocr_report(self):
        """Generate comprehensive OCR analysis report."""
        print("\n" + "="*60)
        print("OCR CONTENT ANALYSIS REPORT")
        print("="*60)
        
        if not self.ocr_captures:
            print("No OCR captures recorded")
            return
        
        total_captures = len(self.ocr_captures)
        problematic_captures = len(self.problematic_patterns)
        
        print(f"Total OCR captures: {total_captures}")
        print(f"Problematic captures: {problematic_captures} ({problematic_captures/total_captures*100:.1f}%)")
        
        # Analyze similarity patterns
        similarities = [c['similarity_to_previous'] for c in self.ocr_captures
                       if c['similarity_to_previous'] is not None]
        if similarities:
            avg_similarity = sum(similarities) / len(similarities)
            min_similarity = min(similarities)
            print(f"Average similarity: {avg_similarity:.2f}")
            print(f"Minimum similarity: {min_similarity:.2f}")
        else:
            print("No similarity data available (all None values)")
        
        # Show most problematic captures
        if self.problematic_patterns:
            print(f"\nMost problematic captures:")
            for i, pattern in enumerate(self.problematic_patterns[-5:], 1):  # Last 5
                print(f"  {i}. {pattern['timestamp']}: {', '.join(pattern['issues'])}")
                print(f"     Raw: '{pattern['raw_preview']}...'")
                print(f"     Normalized: '{pattern['normalized_preview']}...'")

class PerformanceProfiler:
    """Profile performance of critical functions."""
    
    def __init__(self):
        self.function_timings = {}
        self.active_timers = {}
        
    def start_timing(self, function_name):
        """Start timing a function."""
        self.active_timers[function_name] = time.time()
    
    def end_timing(self, function_name):
        """End timing a function and record the duration."""
        if function_name not in self.active_timers:
            print(f"⚠️  Warning: No start time recorded for {function_name}")
            return
        
        duration = time.time() - self.active_timers[function_name]
        del self.active_timers[function_name]
        
        if function_name not in self.function_timings:
            self.function_timings[function_name] = []
        
        self.function_timings[function_name].append(duration)
        
        # Log slow operations
        if duration > 0.1:  # More than 100ms
            print(f"[PERF] ⚠️  SLOW: {function_name} took {duration:.3f}s")
        elif duration > 0.05:  # More than 50ms
            print(f"[PERF] {function_name} took {duration:.3f}s")
    
    def generate_performance_report(self):
        """Generate performance analysis report."""
        print("\n" + "="*60)
        print("PERFORMANCE ANALYSIS REPORT")
        print("="*60)
        
        if not self.function_timings:
            print("No performance data recorded")
            return
        
        for func_name, timings in self.function_timings.items():
            if not timings:
                continue
                
            avg_time = sum(timings) / len(timings)
            max_time = max(timings)
            min_time = min(timings)
            total_time = sum(timings)
            call_count = len(timings)
            
            print(f"\n{func_name}:")
            print(f"  Calls: {call_count}")
            print(f"  Total time: {total_time:.3f}s")
            print(f"  Average: {avg_time:.3f}s")
            print(f"  Min: {min_time:.3f}s")
            print(f"  Max: {max_time:.3f}s")
            
            # Flag performance issues
            if avg_time > 0.1:
                print(f"  ⚠️  PERFORMANCE ISSUE: Average time > 100ms")
            if max_time > 0.5:
                print(f"  ⚠️  PERFORMANCE SPIKE: Max time > 500ms")

# Global instances for easy access
thread_monitor = ThreadSafetyMonitor()
ocr_analyzer = OCRContentAnalyzer()
performance_profiler = PerformanceProfiler()

def save_debug_session(filename=None):
    """Save all debugging data to a file for analysis."""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"autotyper_debug_session_{timestamp}.json"
    
    debug_data = {
        'timestamp': datetime.now().isoformat(),
        'thread_events': list(thread_monitor.thread_events),
        'typing_active_changes': thread_monitor.typing_active_changes,
        'capture_active_changes': thread_monitor.capture_active_changes,
        'ocr_captures': list(ocr_analyzer.ocr_captures),
        'problematic_patterns': ocr_analyzer.problematic_patterns,
        'function_timings': performance_profiler.function_timings
    }
    
    with open(filename, 'w') as f:
        json.dump(debug_data, f, indent=2)
    
    print(f"Debug session saved to: {filename}")
    return filename
