import tkinter as tk
from tkinter import Toplevel, Canvas, filedialog
import pytesseract
import threading
import pyautogui
import random
pyautogui.FAILSAFE = False
import re
from textblob import TextBlob
import time
from PIL import ImageGrab
import difflib
from collections import deque
import tempfile
import os
import atexit
from datetime import datetime

# Import real-world debugging tools with safe fallbacks
try:
    from real_world_debugging_tools import thread_monitor, ocr_analyzer, performance_profiler, save_debug_session
    REAL_WORLD_DEBUGGING = True
    print("Real-world debugging tools enabled")
except ImportError:
    REAL_WORLD_DEBUGGING = False
    print("Real-world debugging tools not available")
    # Create safe fallback objects to prevent "possibly unbound" errors
    class SafeFallback:
        def __getattr__(self, name):
            def safe_method(*args, **kwargs):
                pass  # Do nothing if debugging tools not available
            return safe_method

    thread_monitor = SafeFallback()
    ocr_analyzer = SafeFallback()
    performance_profiler = SafeFallback()

    def save_debug_session(*args, **kwargs):
        return None

# Try to import spell checking library, fall back to basic validation if not available
try:
    from spellchecker import SpellChecker
    SPELL_CHECKER_AVAILABLE = True
except ImportError:
    SPELL_CHECKER_AVAILABLE = False
    print("Warning: pyspellchecker not available. Install with: pip install pyspellchecker")

# Try to import ML word boundary detector
try:
    from ml_word_boundary import MLWordBoundaryDetector
    ML_WORD_BOUNDARY_AVAILABLE = True
except ImportError:
    ML_WORD_BOUNDARY_AVAILABLE = False
    print("Warning: ML word boundary detector not available. Using traditional heuristics.")

# Try to import ML cursor detection
try:
    from ml_cursor_detection import MLCursorDetector
    ML_CURSOR_DETECTION_AVAILABLE = True
except ImportError:
    ML_CURSOR_DETECTION_AVAILABLE = False
    print("Warning: ML cursor detection not available. Using traditional cursor removal.")

class ScreenTextTyper:
    def __init__(self, root):
        self.root = root
        self.root.title("Screen Text Typer")
        self.delay = 0.1
        self.error_rate = 0.05
        self.variance = 0.1

        # Initialize trailing space feature attributes BEFORE GUI setup
        # (needed because setup_gui() references text_stability_threshold)
        self.text_stability_threshold = 1.0  # Seconds to wait for text stability (default reduced to 1.0)

        self.setup_gui()
        self.lock = threading.Lock()  # Lock for pyautogui operations

        # Enhanced state tracking for incremental updates
        self.current_text = ""  # Current text in the selected area
        self.previous_text = ""  # Previous text state for comparison
        self.typed_text = ""  # Text that has been successfully typed
        self.cursor_position = 0  # Current cursor position in the text
        self.text_history = deque(maxlen=10)  # History of recent text states for stability
        self.typing_active = False
        self.capture_active = False

        # Remaining trailing space feature state tracking
        self.last_typing_completion_time = 0  # Timestamp of last typing completion
        self.text_stable_since = 0  # Timestamp when text became stable
        self.trailing_space_added = False  # Whether trailing space was added for current cycle
        self.last_stable_text = ""  # Last stable text for comparison

        # Duplicate line detection state tracking
        self.duplicate_lines_skipped = 0  # Count of duplicate lines skipped
        self.total_lines_processed = 0  # Total lines processed
        self.last_duplicate_detection_time = 0  # Timestamp of last duplicate detection

        # Cursor artifact removal state tracking
        self.cursor_artifacts_removed = 0  # Count of cursor artifacts removed
        self.cursor_removal_enabled = True  # Enable/disable cursor removal
        self.last_cursor_removal_time = 0  # Timestamp of last cursor removal

        # Persistent text tracking state
        self.temp_file_path = None  # Path to temporary session file
        self.cross_capture_duplicates_skipped = 0  # Count of cross-capture duplicates skipped
        self.last_cross_capture_detection_time = 0  # Timestamp of last cross-capture detection
        self.temp_file_created = False  # Whether temp file has been created this session

        # Initialize spell checker for dictionary validation
        self.spell_checker = None
        if SPELL_CHECKER_AVAILABLE:
            try:
                self.spell_checker = SpellChecker()
                print("Dictionary validation enabled with pyspellchecker")
            except Exception as e:
                print(f"Failed to initialize spell checker: {e}")
                self.spell_checker = None
        else:
            print("Dictionary validation using built-in word lists only")

        # Initialize ML word boundary detector
        self.ml_word_boundary_detector = None
        self._last_ml_confidence = 0.0  # Track ML confidence for validation
        self._last_ml_decision = None   # Track ML decision for validation

        if ML_WORD_BOUNDARY_AVAILABLE:
            try:
                self.ml_word_boundary_detector = MLWordBoundaryDetector()
                print("AI/ML word boundary detection enabled")
            except Exception as e:
                print(f"Failed to initialize ML word boundary detector: {e}")
                self.ml_word_boundary_detector = None
        else:
            print("Using traditional word boundary heuristics")

        # Initialize ML cursor detector
        self.ml_cursor_detector = None
        if ML_CURSOR_DETECTION_AVAILABLE:
            try:
                self.ml_cursor_detector = MLCursorDetector()
                print("AI/ML cursor artifact detection enabled")
            except Exception as e:
                print(f"Failed to initialize ML cursor detector: {e}")
                self.ml_cursor_detector = None
        else:
            print("Using traditional cursor artifact removal")

        # Register cleanup function for temporary file
        atexit.register(self.cleanup_temp_file)

        # Register window close event handler for cleanup
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

    def debug_log(self, message, function_name=""):
        """Enhanced debug logging with timestamps for systematic debugging."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]  # Include milliseconds
        if function_name:
            print(f"[{timestamp}] {function_name}: {message}")
        else:
            print(f"[{timestamp}] {message}")

    def setup_gui(self):
        self.select_area_button = tk.Button(self.root, text="Select Area", command=self.select_area)
        self.select_area_button.pack(pady=10)

        self.select_file_button = tk.Button(self.root, text="Select File", command=self.select_file)
        self.select_area_button.pack(pady=10)

        self.delay_label = tk.Label(self.root, text="Typing Delay (seconds):")
        self.delay_label.pack()
        self.delay_entry = tk.Entry(self.root)
        self.delay_entry.insert(0, str(self.delay))
        self.delay_entry.pack()

        self.variance_label = tk.Label(self.root, text="Delay Variance (%):")
        self.variance_label.pack()
        self.variance_entry = tk.Entry(self.root)
        self.variance_entry.insert(0, str(self.variance * 100))
        self.variance_entry.pack()

        self.error_rate_label = tk.Label(self.root, text="Error Rate (%):")
        self.error_rate_label.pack()
        self.error_rate_entry = tk.Entry(self.root)
        self.error_rate_entry.insert(0, str(self.error_rate * 100))
        self.error_rate_entry.pack()

        self.countdown_label = tk.Label(self.root, text="Countdown (seconds):")
        self.countdown_label.pack()
        self.countdown_entry = tk.Entry(self.root)
        self.countdown_entry.insert(0, "3")  # Default countdown of 3 seconds
        self.countdown_entry.pack()

        self.trailing_space_delay_label = tk.Label(self.root, text="Trailing Space Delay (seconds):")
        self.trailing_space_delay_label.pack()
        self.trailing_space_delay_entry = tk.Entry(self.root)
        self.trailing_space_delay_entry.insert(0, str(self.text_stability_threshold))
        self.trailing_space_delay_entry.pack()

        self.text_preview_label = tk.Label(self.root, text="Text Preview:")
        self.text_preview_label.pack()
        self.text_preview = tk.Text(self.root, height=10, width=50)
        self.text_preview.pack()

        self.save_text_button = tk.Button(self.root, text="Save Text", command=self.save_text)
        self.save_text_button.pack(pady=10)

        self.check_spelling_button = tk.Button(self.root, text="Check Spelling", command=self.check_spelling)                                                    
        self.check_spelling_button.pack(pady=10)

        self.start_button = tk.Button(self.root, text="Start Typing", command=self.start_typing)
        self.select_file_button.pack(pady=10)

        self.stop_button = tk.Button(self.root, text="Pause Typing", command=self.pause_typing)
        self.stop_button.pack(pady=10)
        self.start_button.pack(pady=10)

        self.status_button = tk.Button(self.root, text="Show Status", command=self.show_status)
        self.status_button.pack(pady=10)

        self.exit_button = tk.Button(self.root, text="Exit", command=self.exit_program)
        self.exit_button.pack(pady=10)

    def select_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            with open(file_path, 'r') as file:
                self.file_text = file.read()

    def select_area(self):
        self.selection_window = Toplevel(self.root)
        self.selection_window.geometry(f"{self.root.winfo_screenwidth()}x{self.root.winfo_screenheight()}+0+0")
        self.selection_window.attributes("-fullscreen", True)
        self.selection_window.wait_visibility()
        self.selection_window.wm_attributes("-alpha", 0.01)  # Almost fully transparent
        #self.selection_window.attributes("-topmost", True)
        self.selection_window.overrideredirect(True)
        self.selection_window.bind("<Escape>", lambda _: self.selection_window.destroy())

        self.canvas = Canvas(self.selection_window, cursor="cross")
        self.canvas.pack(fill="both", expand=True)
        self.canvas.bind("<ButtonPress-1>", self.on_button_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_button_release)

        self.start_x = self.start_y = 0
        self.rect = None

    def on_button_press(self, event):
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)
        self.rect = self.canvas.create_rectangle(self.start_x, self.start_y, self.start_x, self.start_y, outline='red')

    def on_mouse_drag(self, event):
        cur_x, cur_y = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
        self.canvas.coords(self.rect, self.start_x, self.start_y, cur_x, cur_y)

    def on_button_release(self, event):
        end_x, end_y = (self.canvas.canvasx(event.x), self.canvas.canvasy(event.y))
        self.area = (int(self.start_x), int(self.start_y), int(end_x), int(end_y))
        self.selection_window.destroy()
        self.text_preview.delete(1.0, tk.END)
        self.text_preview.insert(tk.END, pytesseract.image_to_string(ImageGrab.grab(bbox=(self.start_x, self.start_y, end_x, end_y))))

    def save_text(self):
        self.file_text = self.text_preview.get(1.0, tk.END).strip()
        print("Saved Text Preview:")
        print(self.file_text)

    def check_spelling(self):
        text = self.text_preview.get(1.0, tk.END).strip()
        blob = TextBlob(text)
        misspelled_words = [word for word in blob.words if word != word.correct()]
        if misspelled_words:
            print("Misspelled Words:")
            for word in misspelled_words:
                print(f"{word} -> {word.correct()}")
        else:
            print("No spelling errors found.")

    def normalize_text(self, text):
        """Normalize text to handle OCR inconsistencies and text wrapping with dictionary validation."""
        if not text:
            return ""

        # First, apply ML cursor detection if enabled and available
        if self.cursor_removal_enabled and self.ml_cursor_detector:
            try:
                text, corrections = self.ml_cursor_detector.detect_and_correct_cursor_artifacts(text)
                if corrections:
                    print(f"ML cursor detection applied {len(corrections)} corrections")
                    for correction in corrections:
                        print(f"  '{correction['original']}' → '{correction['corrected']}' (confidence: {correction['confidence']:.2f})")
            except Exception as e:
                print(f"ML cursor detection failed: {e}")

        # Then, apply traditional cursor removal as fallback/supplement
        if self.cursor_removal_enabled:
            text = self.remove_cursor_artifacts(text)

        # Handle line breaks intelligently
        normalized = self.handle_line_breaks_with_validation(text)

        # Remove extra whitespace (but preserve single spaces)
        normalized = re.sub(r'[ \t]+', ' ', normalized)

        # Remove common OCR artifacts
        normalized = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', '', normalized)

        # Final cleanup - strip leading/trailing whitespace
        return normalized.strip()

    def handle_line_breaks(self, text):
        """Intelligently handle line breaks to distinguish between wrapping and actual breaks."""
        if not text:
            return ""

        # Split text into lines, preserving original line content
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            if i == 0:
                # First line - add as-is but strip trailing whitespace
                result.append(line.rstrip())
                continue

            if not line.strip():
                # Empty line - could be paragraph break
                if i < len(lines) - 1 and lines[i + 1].strip():
                    # Empty line followed by content - treat as paragraph break
                    result.append(' ')
                continue

            # Get the original previous line (before any processing)
            prev_line_original = lines[i - 1]
            prev_line_stripped = prev_line_original.rstrip()
            current_line_stripped = line.lstrip()

            if not prev_line_stripped:
                # Previous line was empty, start new content
                result.append(' ' + current_line_stripped)
                continue

            # Check if previous line ended with space (indicating intentional break)
            prev_had_trailing_space = len(prev_line_original) > len(prev_line_stripped)
            current_has_leading_space = len(line) > len(current_line_stripped)

            if prev_had_trailing_space or current_has_leading_space:
                # There was intentional spacing - preserve it
                result.append(' ' + current_line_stripped)
            elif self.is_word_wrapped(prev_line_stripped, current_line_stripped):
                # Word was wrapped - join without space
                result.append(current_line_stripped)
            else:
                # Normal line break - add space
                result.append(' ' + current_line_stripped)

        return ''.join(result)

    def handle_line_breaks_with_validation(self, text):
        """Handle line breaks with dictionary validation for improved accuracy."""
        if not text:
            return ""

        # Split text into lines, preserving original line content
        lines = text.split('\n')
        result = []

        for i, line in enumerate(lines):
            if i == 0:
                # First line - add as-is but strip trailing whitespace
                result.append(line.rstrip())
                continue

            if not line.strip():
                # Empty line - could be paragraph break
                if i < len(lines) - 1 and lines[i + 1].strip():
                    # Empty line followed by content - treat as paragraph break
                    result.append(' ')
                continue

            # Get the original previous line (before any processing)
            prev_line_original = lines[i - 1]
            prev_line_stripped = prev_line_original.rstrip()
            current_line_stripped = line.lstrip()

            if not prev_line_stripped:
                # Previous line was empty, start new content
                result.append(' ' + current_line_stripped)
                continue

            # Check if previous line ended with space (indicating intentional break)
            prev_had_trailing_space = len(prev_line_original) > len(prev_line_stripped)
            current_has_leading_space = len(line) > len(current_line_stripped)

            if prev_had_trailing_space or current_has_leading_space:
                # There was intentional spacing - preserve it
                result.append(' ' + current_line_stripped)
            else:
                # Use enhanced word boundary detection with dictionary validation
                should_join = self.is_word_wrapped_with_validation(prev_line_stripped, current_line_stripped)

                if should_join:
                    result.append(current_line_stripped)
                else:
                    result.append(' ' + current_line_stripped)

        return ''.join(result)

    def is_word_wrapped(self, prev_line, current_line):
        """Determine if a line break represents word wrapping using enhanced heuristics."""
        if not prev_line or not current_line:
            return False

        # Get the last word of previous line and first word of current line
        prev_words = prev_line.strip().split()
        current_words = current_line.strip().split()

        if not prev_words or not current_words:
            return False

        last_word = prev_words[-1]
        first_word = current_words[0]

        # Get the last character of previous line and first character of current line
        last_char = prev_line.rstrip()[-1] if prev_line.rstrip() else ''
        first_char = current_line.lstrip()[0] if current_line.lstrip() else ''

        # If previous line ends with space or current line starts with space, not wrapped
        if last_char.isspace() or first_char.isspace():
            return False

        # If previous line ends with punctuation, likely not wrapped
        if last_char in '.!?;:,':
            return False

        # If current line starts with punctuation, likely not wrapped
        if first_char in '.!?;:,':
            return False

        # If previous line ends with hyphen, likely hyphenated word
        if last_char == '-':
            return True

        # Enhanced semantic analysis
        return self.analyze_word_boundary(last_word, first_word)

    def is_word_wrapped_with_validation(self, prev_line, current_line):
        """Enhanced word wrapping detection with dictionary validation."""
        if not prev_line or not current_line:
            return False

        # Get the last word of previous line and first word of current line
        prev_words = prev_line.strip().split()
        current_words = current_line.strip().split()

        if not prev_words or not current_words:
            return False

        last_word = prev_words[-1]
        first_word = current_words[0]

        # Get the last character of previous line and first character of current line
        last_char = prev_line.rstrip()[-1] if prev_line.rstrip() else ''
        first_char = current_line.lstrip()[0] if current_line.lstrip() else ''

        # Basic checks first
        if last_char.isspace() or first_char.isspace():
            return False
        if last_char in '.!?;:,':
            return False
        if first_char in '.!?;:,':
            return False
        if last_char == '-':
            return True

        # Get initial decision from semantic analysis
        semantic_decision = self.analyze_word_boundary(last_word, first_word)

        # Apply dictionary validation to improve accuracy
        return self.validate_word_boundary_decision(last_word, first_word, semantic_decision)

    def analyze_word_boundary(self, last_word, first_word):
        """Analyze whether two words should be joined or separated using AI/ML or semantic heuristics."""

        # Try ML approach first if available
        if self.ml_word_boundary_detector:
            try:
                should_join, confidence = self.ml_word_boundary_detector.should_join_words(last_word, first_word)

                # Store ML confidence for validation layer
                self._last_ml_confidence = confidence
                self._last_ml_decision = should_join

                # Use ML result if confidence is high enough
                if confidence > 0.6:
                    print(f"ML word boundary decision: '{last_word}' + '{first_word}' = {should_join} (confidence: {confidence:.2f})")
                    return should_join
                else:
                    print(f"ML confidence too low ({confidence:.2f}), falling back to heuristics")
            except Exception as e:
                print(f"ML word boundary detection failed: {e}")
                self._last_ml_confidence = 0.0
                self._last_ml_decision = None
        else:
            self._last_ml_confidence = 0.0
            self._last_ml_decision = None

        # Fallback to traditional heuristic approach
        return self._traditional_analyze_word_boundary(last_word, first_word)

    def _traditional_analyze_word_boundary(self, last_word, first_word):
        """Traditional heuristic-based word boundary analysis (fallback)."""

        # Check for obvious word continuation patterns first (highest priority)
        if self.is_likely_word_continuation(last_word, first_word):
            return True

        # Check for common compound word patterns (high priority)
        if self.is_likely_compound_word(last_word, first_word):
            return True

        # Check word length and breaking patterns
        if self.suggests_line_wrap(last_word, first_word):
            return True

        # Check for capitalization patterns that suggest separate words
        if self.suggests_separate_words(last_word, first_word):
            return False

        # Default to separate words (safer approach)
        return False

    def is_likely_word_continuation(self, last_word, first_word):
        """Check if the second word is likely a continuation of the first."""

        # Common suffix patterns that suggest word continuation
        common_suffixes = {
            'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment',
            'able', 'ible', 'ful', 'less', 'ous', 'ive', 'al', 'ic', 'ical'
        }

        # If the first word looks incomplete and second word is a common suffix
        if (len(last_word) <= 3 and
            first_word.lower() in common_suffixes and
            not last_word.endswith(('ing', 'ed', 'er', 'ly'))):
            return True

        # Check for common prefixes that suggest word continuation
        if (len(last_word) <= 3 and
            last_word.lower() in ('un', 're', 'pre', 'dis', 'mis', 'in', 'ex', 'de')):
            return True

        # Enhanced logic for very short fragments that don't form valid words
        # Only apply this logic if BOTH words are not complete common words
        # This prevents joining legitimate word pairs like "good morning"
        if (not self.is_complete_common_word(last_word) and
            not self.is_complete_common_word(first_word)):

            # If the last word is very short (1-2 chars) and not a valid standalone word
            if (len(last_word) <= 2 and
                not self.is_valid_word(last_word)):
                # But don't join if the last word is a common article or preposition
                if last_word.lower() not in {'a', 'i', 'to', 'of', 'in', 'on', 'at', 'by', 'or'}:
                    # Check if combining creates a valid word
                    combined = last_word + first_word
                    if self.is_valid_word(combined):
                        return True

            # If the first word is a short fragment that doesn't form a valid word
            if (len(first_word) <= 3 and
                not self.is_valid_word(first_word)):
                # But don't join if the first word is a common word ending
                if first_word.lower() not in {'car', 'day', 'way', 'man', 'boy', 'run', 'sun', 'fun'}:
                    # Check if combining creates a valid word
                    combined = last_word + first_word
                    if self.is_valid_word(combined):
                        return True

        # Check if joining creates a recognizable word pattern
        # But only if both words are not complete common words (to avoid joining "good morning")
        if (not self.is_complete_common_word(last_word) or
            not self.is_complete_common_word(first_word)):
            combined = last_word.lower() + first_word.lower()
            if self.is_recognizable_word_pattern(combined):
                return True

        return False

    def suggests_separate_words(self, last_word, first_word):
        """Check if capitalization or other patterns suggest separate words."""

        # If the second word starts with a capital letter and the first doesn't end with hyphen
        # it's likely a new word/sentence
        if (first_word[0].isupper() and
            len(last_word) > 1 and
            not last_word.endswith('-') and
            not last_word.isupper()):  # Unless it's an acronym
            return True

        # If both words are capitalized, likely proper nouns or separate words
        if (last_word[0].isupper() and first_word[0].isupper() and
            len(last_word) > 2 and len(first_word) > 2):
            return True

        # If the last word is a complete common word AND not part of a compound
        if (self.is_complete_common_word(last_word) and
            not self.is_likely_compound_word(last_word, first_word)):
            return True

        return False

    def is_likely_compound_word(self, last_word, first_word):
        """Check if the words form a likely compound word."""

        # Common compound word patterns
        compound_patterns = {
            # First part of compound words
            'some': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'any': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'every': ['thing', 'where', 'how', 'one', 'body', 'time'],
            'no': ['thing', 'where', 'how', 'one', 'body'],
            'under': ['stand', 'ground', 'water', 'line', 'way'],
            'over': ['come', 'look', 'head', 'time', 'flow'],
            'out': ['side', 'put', 'come', 'look', 'line', 'door', 'going'],
            'up': ['date', 'grade', 'load', 'set', 'ward', 'stairs', 'hill'],
            'down': ['load', 'ward', 'hill', 'town', 'fall', 'stairs'],
            'good': ['bye', 'will', 'ness'],  # Note: "morning" deliberately excluded
            'after': ['noon', 'ward', 'math'],
            'with': ['out', 'in'],
        }

        last_lower = last_word.lower()
        first_lower = first_word.lower()

        if last_lower in compound_patterns:
            if first_lower in compound_patterns[last_lower]:
                return True

        return False

    def suggests_line_wrap(self, last_word, first_word):
        """Check if word length and patterns suggest line wrapping."""

        # If the last word is very short (1-2 chars) and looks like a prefix
        if (len(last_word) <= 2 and
            last_word.lower() in ['a', 'an', 'be', 'de', 're', 'un', 'in', 'ex']):
            return True

        # If the first word is very short and looks like a suffix
        if (len(first_word) <= 3 and
            first_word.lower() in ['ing', 'ed', 'er', 'ly', 'al', 'ic']):
            return True

        # Enhanced logic for arbitrary word splits
        # Only apply this logic if BOTH words are not complete common words
        # This prevents joining legitimate word pairs like "good morning"
        if (not self.is_complete_common_word(last_word) and
            not self.is_complete_common_word(first_word)):

            # If the last word is very short (1-2 chars) and doesn't form a valid word by itself
            if (len(last_word) <= 2 and
                not self.is_valid_word(last_word)):
                # But don't join if the last word is a common article or preposition
                if last_word.lower() not in {'a', 'i', 'to', 'of', 'in', 'on', 'at', 'by', 'or'}:
                    # Check if combining creates a valid word
                    combined = last_word + first_word
                    if self.is_valid_word(combined) or len(combined) >= 4:
                        return True

            # If the first word is very short (1-3 chars) and doesn't form a valid word by itself
            if (len(first_word) <= 3 and
                not self.is_valid_word(first_word)):
                # But don't join if the first word is a common word ending
                if first_word.lower() not in {'car', 'day', 'way', 'man', 'boy', 'run', 'sun', 'fun'}:
                    # Check if combining creates a valid word
                    combined = last_word + first_word
                    if self.is_valid_word(combined):
                        return True

            # Special case: if last word is 1-2 chars and first word is longer,
            # and the last word is not a complete word, likely wrapped
            if (len(last_word) <= 2 and len(first_word) >= 3):
                # But don't join if the last word is a common article or preposition
                if last_word.lower() not in {'a', 'i', 'to', 'of', 'in', 'on', 'at', 'by', 'or'}:
                    combined = last_word + first_word
                    # If the combined word is reasonable length and could be valid
                    if len(combined) >= 4 and len(combined) <= 15:
                        return True

        return False

    def is_recognizable_word_pattern(self, combined_word):
        """Check if the combined word follows recognizable English patterns."""

        # Common word patterns and endings
        if len(combined_word) < 4:
            return False

        # Check for common word endings
        common_endings = [
            'tion', 'sion', 'ness', 'ment', 'able', 'ible', 'ful', 'less',
            'ous', 'ive', 'ing', 'ed', 'er', 'est', 'ly', 'al', 'ic', 'ical'
        ]

        for ending in common_endings:
            if combined_word.endswith(ending) and len(combined_word) > len(ending) + 2:
                return True

        return False

    def is_complete_common_word(self, word):
        """Check if a word is a complete common English word."""

        # List of very common complete words that are unlikely to be partial
        common_complete_words = {
            'a', 'an', 'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy',
            'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use', 'word', 'work',
            'first', 'would', 'there', 'could', 'water', 'after', 'back', 'other',
            'many', 'them', 'these', 'come', 'made', 'most', 'over', 'said', 'some',
            'time', 'very', 'when', 'much', 'before', 'here', 'through', 'just',
            'where', 'much', 'good', 'sentence', 'right', 'think', 'great', 'help',
            'hello', 'world', 'people', 'place', 'year', 'years', 'state', 'show',
            'every', 'good', 'those', 'feel', 'life', 'fact', 'hand', 'high',
            'number', 'part', 'point', 'tell', 'want', 'because', 'same', 'turn',
            'three', 'small', 'large', 'next', 'early', 'young', 'important', 'few',
            'public', 'bad', 'same', 'able', 'morning', 'evening', 'night', 'today',
            'tomorrow', 'yesterday', 'week', 'month', 'car', 'house', 'door', 'window'
        }

        return word.lower() in common_complete_words

    def validate_word_boundary_decision(self, last_word, first_word, semantic_decision):
        """Validate word boundary decision using dictionary lookup and confidence scoring."""

        # Check if we have a high-confidence ML decision that should be respected
        if hasattr(self, '_last_ml_confidence') and hasattr(self, '_last_ml_decision'):
            if self._last_ml_confidence > 0.75 and self._last_ml_decision is not None:
                print(f"Respecting high-confidence ML decision: {self._last_ml_decision} (ML confidence: {self._last_ml_confidence:.2f})")
                return self._last_ml_decision

        # Calculate confidence score for the semantic decision
        confidence = self.calculate_decision_confidence(last_word, first_word, semantic_decision)

        # If confidence is high, trust the semantic decision
        if confidence >= 0.8:
            return semantic_decision

        # For low confidence decisions, use dictionary validation
        if semantic_decision:
            # Semantic analysis suggests joining - validate the result
            return self.validate_word_joining(last_word, first_word)
        else:
            # Semantic analysis suggests separating - validate the fragments
            return self.validate_word_separation(last_word, first_word)

    def calculate_decision_confidence(self, last_word, first_word, decision):
        """Calculate confidence score for a word boundary decision."""
        confidence = 0.5  # Base confidence

        # High confidence indicators for joining
        if decision:
            # Hyphenated words
            if last_word.endswith('-'):
                confidence += 0.4

            # Common suffixes
            if first_word.lower() in {'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion'}:
                confidence += 0.3

            # Common prefixes
            if last_word.lower() in {'un', 're', 'pre', 'dis', 'mis', 'in', 'ex'}:
                confidence += 0.3

            # Known compound patterns
            if self.is_likely_compound_word(last_word, first_word):
                confidence += 0.2

        # High confidence indicators for separating
        else:
            # Capitalization suggests new word
            if first_word[0].isupper() and not last_word.endswith('-'):
                confidence += 0.3

            # Both words are complete common words
            if (self.is_complete_common_word(last_word) and
                self.is_complete_common_word(first_word)):
                confidence += 0.2

        return min(confidence, 1.0)

    def validate_word_joining(self, last_word, first_word):
        """Validate whether joining two words creates a valid word."""
        combined_word = last_word + first_word

        # Check if the combined word is valid
        if self.is_valid_word(combined_word):
            return True

        # Check if either part alone is invalid (suggesting they should be joined)
        last_word_valid = self.is_valid_word(last_word)
        first_word_valid = self.is_valid_word(first_word)

        # If both parts are invalid separately, likely they should be joined
        if not last_word_valid and not first_word_valid:
            return True

        # If one part is invalid and the other is very short, likely joining
        if (not last_word_valid and len(first_word) <= 3) or (not first_word_valid and len(last_word) <= 3):
            return True

        # Default to not joining if combined word is invalid
        return False

    def validate_word_separation(self, last_word, first_word):
        """Validate whether separating words is correct."""
        combined_word = last_word + first_word

        # If the combined word would be invalid, separation is correct
        if not self.is_valid_word(combined_word):
            return False

        # If both parts are valid separately, separation is likely correct
        if self.is_valid_word(last_word) and self.is_valid_word(first_word):
            return False

        # If combined word is valid but parts aren't, should probably join
        return True

    def is_valid_word(self, word):
        """Check if a word is valid using available dictionary resources."""
        if not word:
            return False

        word_lower = word.lower()

        # Check against built-in common words first (includes single-letter words like 'a', 'i')
        if self.is_complete_common_word(word):
            return True

        # For other words, require at least 2 characters
        if len(word) < 2:
            return False

        # Check against extended built-in dictionary
        if self.is_in_extended_dictionary(word_lower):
            return True

        # Use spell checker if available
        if self.spell_checker:
            try:
                return word_lower in self.spell_checker
            except Exception:
                pass

        # Fallback: check if it follows common English patterns
        return self.follows_english_patterns(word_lower)

    def remove_cursor_artifacts(self, text):
        """Remove cursor artifacts from OCR text using intelligent pattern detection."""
        if not text:
            return text

        original_text = text

        # Apply cursor removal patterns in the right order
        # 1. First remove repeated patterns (most obvious artifacts)
        text = self.remove_repeated_vertical_patterns(text)
        # 2. Then clean word-internal cursors (but preserve word boundaries)
        text = self.clean_all_word_internal_cursors(text)
        # 3. Then remove standalone vertical bars (including word boundaries)
        text = self.remove_standalone_vertical_bars(text)
        # 4. Finally remove cursor misdetections
        text = self.remove_cursor_misdetections(text)

        # Update statistics if any changes were made
        if text != original_text:
            self.cursor_artifacts_removed += 1
            self.last_cursor_removal_time = time.time()
            print(f"Cursor artifacts removed: '{original_text}' -> '{text}'")

        return text

    def clean_all_word_internal_cursors(self, text):
        """Clean cursor artifacts within all words before processing word boundaries."""
        if not text:
            return text

        # Process line by line to preserve newlines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            # Process words within each line
            words = line.split()
            cleaned_words = []

            for word in words:
                cleaned_word = self.clean_word_internal_cursors(word)
                cleaned_words.append(cleaned_word)

            cleaned_lines.append(' '.join(cleaned_words))

        return '\n'.join(cleaned_lines)

    def remove_standalone_vertical_bars(self, text):
        """Remove standalone vertical bars that appear between words or at boundaries."""
        import re
        if not text:
            return text

        # Pattern 1: Vertical bar surrounded by spaces (but preserve newlines)
        # Example: "hello | world" -> "hello world"
        # Don't treat newlines as generic whitespace to preserve line structure
        text = re.sub(r'[ \t]+\|[ \t]+', ' ', text)

        # Pattern 2: Vertical bar at word boundaries (but not within words)
        # Example: "hello|world" -> "hello world" but not "wo|r|ld" -> "wo r ld"
        # Only apply if the vertical bar is between what look like complete word parts
        def replace_word_boundary_cursor(match):
            # Get the context around the match
            start_pos = max(0, match.start() - 20)
            end_pos = min(len(text), match.end() + 20)
            context = text[start_pos:end_pos]

            # Check if this looks like a word boundary vs internal cursor
            # If there are multiple | characters nearby, it's likely internal
            cursor_count = context.count('|')
            if cursor_count > 1:
                return match.group(0)  # Keep original, let word internal handle it

            return f'{match.group(1)} {match.group(2)}'

        text = re.sub(r'(\w)\|(\w)', replace_word_boundary_cursor, text)

        # Pattern 3: Vertical bar at line start/end (preserve newlines)
        # Example: "|hello world" -> "hello world"
        text = re.sub(r'^\|[ \t]*', '', text, flags=re.MULTILINE)
        text = re.sub(r'[ \t]*\|$', '', text, flags=re.MULTILINE)

        # Pattern 4: Multiple vertical bars in sequence
        # Example: "hello || world" -> "hello world"
        text = re.sub(r'\|+', ' ', text)

        return text

    def remove_cursor_misdetections(self, text):
        """Remove characters that may be cursor misdetections in unexpected contexts."""
        if not text:
            return text

        # Process line by line to preserve newlines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            # Split into words for context analysis within each line
            words = line.split()
            cleaned_words = []

            for i, word in enumerate(words):
                # Check for single "I" or "l" that might be cursor artifacts
                if self.is_likely_cursor_misdetection(word, i, words):
                    # Skip this word (remove the cursor artifact)
                    continue
                else:
                    # Keep the word as-is (word internal cursors already cleaned)
                    cleaned_words.append(word)

            cleaned_lines.append(' '.join(cleaned_words))

        return '\n'.join(cleaned_lines)

    def is_likely_cursor_misdetection(self, word, position, all_words):
        """Determine if a single character word is likely a cursor misdetection."""
        # Only consider single character words
        if len(word) != 1:
            return False

        char = word.lower()

        # Don't remove legitimate single letter words
        legitimate_single_chars = {'a', 'i'}  # "a" and "I" are legitimate
        if char in legitimate_single_chars:
            return False

        # Check for cursor-like characters in unexpected positions
        cursor_chars = {'|', 'l', '1', 'i'}
        if char not in cursor_chars:
            return False

        # Context analysis: cursor artifacts often appear at word boundaries
        # or in positions where they don't make grammatical sense

        # If it's between two words that would make sense together
        if 0 < position < len(all_words) - 1:
            prev_word = all_words[position - 1].lower()
            next_word = all_words[position + 1].lower()

            # If removing this character would create a sensible phrase
            if self.would_make_sense_without_cursor(prev_word, next_word):
                return True

        # If it's at the beginning or end and looks like a cursor
        if position == 0 or position == len(all_words) - 1:
            if char in {'|', 'l', '1'}:  # More likely to be cursor at boundaries
                return True

        return False

    def would_make_sense_without_cursor(self, prev_word, next_word):
        """Check if removing a cursor between two words would make sense."""
        # Common word pairs that make sense when joined
        sensible_pairs = [
            # Articles and nouns
            ('the', 'house'), ('a', 'car'), ('an', 'apple'),
            # Prepositions and nouns
            ('in', 'the'), ('on', 'top'), ('at', 'home'),
            # Common phrases
            ('hello', 'world'), ('thank', 'you'), ('good', 'morning'),
            # Verbs and objects
            ('read', 'book'), ('write', 'text'), ('open', 'door'),
        ]

        # Check if this is a known sensible pair
        pair = (prev_word, next_word)
        if pair in sensible_pairs:
            return True

        # Check for common grammatical patterns
        # Article + noun
        if prev_word in {'the', 'a', 'an'} and len(next_word) > 2:
            return True

        # Preposition + article/noun
        if prev_word in {'in', 'on', 'at', 'to', 'for', 'with', 'by'} and next_word in {'the', 'a', 'an'} or len(next_word) > 2:
            return True

        return False

    def clean_word_internal_cursors(self, text):
        """Remove cursor artifacts that appear within words, but preserve word boundaries."""
        import re
        if len(text) <= 1:
            return text

        # Remove vertical bars that are clearly within a single word and make sense
        # Example: "hel|lo" -> "hello" (if it creates a valid word)
        # But preserve "hello|world" for later word boundary processing

        # Check if this looks like two complete words joined by a cursor
        if '|' in text:
            parts = text.split('|')
            if len(parts) == 2 and len(parts[0]) >= 3 and len(parts[1]) >= 3:
                # This might be two words joined by cursor - check if they look like complete words,
                # or if they look like filenames, URLs, or other structured content
                if (self.looks_like_complete_word(parts[0]) and
                    self.looks_like_complete_word(parts[1])) or \
                   self.looks_like_structured_content(parts[0], parts[1]):
                    # Let the standalone vertical bar removal handle it
                    return text

        return text

    def looks_like_structured_content(self, part1, part2):
        """Check if two parts look like structured content (filenames, URLs, etc.)."""
        import re
        # Check for file extensions
        if '.' in part1 and len(part1.split('.')[-1]) <= 4:  # Common file extension
            return True

        # Check for common structured patterns
        structured_patterns = [
            # File extensions
            r'.*\.(txt|doc|pdf|jpg|png|gif|html|css|js|py|java|cpp|exe|zip)$',
            # URLs or domains
            r'.*(www|http|https|ftp).*',
            # Email-like patterns
            r'.*@.*',
            # Version numbers
            r'.*v?\d+\.\d+.*',
            # IDs or codes
            r'.*[A-Z]{2,}\d+.*',
        ]

        combined = part1 + part2
        for pattern in structured_patterns:
            if re.match(pattern, combined, re.IGNORECASE):
                return True

        return False

    def looks_like_complete_word(self, word):
        """Check if a word fragment looks like it could be a complete word."""
        if len(word) < 3:
            return False

        # Check if it's a known complete word
        if self.is_complete_common_word(word):
            return True

        # Check if it follows common English patterns
        if self.follows_english_patterns(word):
            return True

        # Check if it has vowels (most English words have vowels)
        vowels = set('aeiouAEIOU')
        if not any(c in vowels for c in word):
            return False

        # If it's reasonably long and has vowels, might be a word
        return len(word) >= 4

    def remove_repeated_vertical_patterns(self, text):
        """Remove repeated vertical line patterns that don't form valid words."""
        import re
        if not text:
            return text

        # Pattern 1: Multiple vertical bars in sequence
        # Example: "|||" -> " "
        text = re.sub(r'\|{2,}', ' ', text)

        # Pattern 2: Alternating vertical bars and spaces
        # Example: "| | |" -> " "
        text = re.sub(r'(\|\s*){2,}', ' ', text)

        # Pattern 3: Only remove obvious cursor character sequences (4+ chars)
        # Example: "l|l|l|l" -> " " but not "hel|lo"
        text = re.sub(r'[l1i\|]{4,}', ' ', text)

        return text

    def is_in_extended_dictionary(self, word):
        """Check against an extended built-in dictionary of common English words."""
        # Extended dictionary of common English words
        extended_words = {
            # Common verbs
            'walk', 'walking', 'walked', 'run', 'running', 'ran', 'jump', 'jumping', 'jumped',
            'think', 'thinking', 'thought', 'speak', 'speaking', 'spoke', 'write', 'writing', 'wrote',
            'read', 'reading', 'look', 'looking', 'looked', 'find', 'finding', 'found',

            # Common nouns
            'house', 'car', 'book', 'table', 'chair', 'computer', 'phone', 'door', 'window',
            'tree', 'flower', 'animal', 'person', 'child', 'children', 'family', 'friend',
            'school', 'teacher', 'student', 'class', 'lesson', 'homework', 'test', 'exam',

            # Common adjectives
            'good', 'bad', 'big', 'small', 'large', 'little', 'old', 'new', 'young', 'beautiful',
            'ugly', 'happy', 'sad', 'angry', 'excited', 'tired', 'hungry', 'thirsty',

            # Common compound words
            'something', 'anything', 'everything', 'nothing', 'someone', 'anyone', 'everyone',
            'somewhere', 'anywhere', 'everywhere', 'nowhere', 'understand', 'overcome',
            'outside', 'inside', 'upstairs', 'downstairs', 'update', 'download', 'upload',
            'background', 'foreground', 'playground', 'classroom', 'bedroom', 'bathroom',

            # Technical terms
            'computer', 'software', 'hardware', 'internet', 'website', 'email', 'password',
            'database', 'program', 'application', 'system', 'network', 'server', 'client',

            # Common words that might be split
            'through', 'finally', 'really', 'actually', 'probably', 'definitely', 'certainly',
            'beautiful', 'wonderful', 'terrible', 'horrible', 'amazing', 'incredible',
            'important', 'different', 'difficult', 'possible', 'impossible', 'available',
            'necessary', 'interesting', 'comfortable', 'uncomfortable', 'responsible',

            # Additional common words that are often split by OCR
            'yahoo', 'google', 'closet', 'pocket', 'market', 'basket', 'ticket', 'socket',
            'budget', 'target', 'object', 'subject', 'project', 'protect', 'perfect',
            'direct', 'select', 'collect', 'connect', 'correct', 'respect', 'expect',
        }

        return word in extended_words

    def follows_english_patterns(self, word):
        """Check if a word follows common English patterns."""
        if len(word) < 3:
            return False

        # Common English word patterns
        common_patterns = [
            r'^[a-z]+ing$',      # -ing words
            r'^[a-z]+ed$',       # -ed words
            r'^[a-z]+er$',       # -er words
            r'^[a-z]+est$',      # -est words
            r'^[a-z]+ly$',       # -ly words
            r'^[a-z]+tion$',     # -tion words
            r'^[a-z]+sion$',     # -sion words
            r'^[a-z]+ness$',     # -ness words
            r'^[a-z]+ment$',     # -ment words
            r'^[a-z]+able$',     # -able words
            r'^[a-z]+ible$',     # -ible words
            r'^[a-z]+ful$',      # -ful words
            r'^[a-z]+less$',     # -less words
            r'^un[a-z]+$',       # un- words
            r'^re[a-z]+$',       # re- words
            r'^pre[a-z]+$',      # pre- words
        ]

        for pattern in common_patterns:
            if re.match(pattern, word):
                return True

        return False

    def calculate_text_similarity(self, text1, text2):
        """Calculate similarity between two text strings."""
        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0

        # Use sequence matcher to get similarity ratio
        matcher = difflib.SequenceMatcher(None, text1, text2)
        return matcher.ratio()

    def get_stable_text(self, new_text):
        """Get stable text by comparing with recent history."""
        normalized_text = self.normalize_text(new_text)

        # Add to history
        self.text_history.append(normalized_text)

        # If we don't have enough history, return the normalized text
        if len(self.text_history) < 3:
            return normalized_text

        # Find the most consistent text in recent history
        recent_texts = list(self.text_history)[-3:]
        best_text = normalized_text
        best_score = 0

        for candidate in recent_texts:
            total_similarity = sum(self.calculate_text_similarity(candidate, other)
                                 for other in recent_texts)
            if total_similarity > best_score:
                best_score = total_similarity
                best_text = candidate

        return best_text

    def calculate_append_suffix(self, typed_text, current_text):
        """Calculate the suffix that needs to be appended (append-only logic)."""
        if not current_text:
            return ""

        if not typed_text:
            return current_text

        # Check if current text starts with typed text
        if current_text.startswith(typed_text):
            # Return the new suffix that needs to be typed
            return current_text[len(typed_text):]
        else:
            # Text was reset or changed - return entire current text
            return current_text

    def detect_duplicate_lines(self, typed_text, current_text):
        """Detect complete duplicate lines between typed text and current text.

        Returns:
            tuple: (duplicate_lines_count, resume_position, skipped_content)
        """
        if not typed_text or not current_text:
            return 0, 0, ""

        # Split both texts into lines
        typed_lines = typed_text.split('\n')
        current_lines = current_text.split('\n')

        duplicate_count = 0
        resume_position = 0
        skipped_content = ""

        # Compare lines from the beginning
        min_lines = min(len(typed_lines), len(current_lines))

        for i in range(min_lines):
            typed_line = typed_lines[i].strip()
            current_line = current_lines[i].strip()

            # Check if lines are identical and contain multiple words (not single words)
            if (typed_line == current_line and
                len(typed_line.split()) > 1 and  # Must have multiple words
                len(typed_line) > 0):  # Must not be empty

                duplicate_count += 1
                # Calculate position after this line
                # Sum up lengths of all lines up to and including current line, plus newlines
                resume_position = 0
                for j in range(i + 1):
                    resume_position += len(current_lines[j])
                    if j < i:  # Add newline after each line except the current one
                        resume_position += 1

                # If there are more lines after this duplicate, add newline after current line
                if i < len(current_lines) - 1:
                    resume_position += 1

                # Add to skipped content
                if skipped_content:
                    skipped_content += '\n'
                skipped_content += current_line

            else:
                # Lines differ, stop checking
                break

        return duplicate_count, resume_position, skipped_content

    def calculate_smart_append_suffix(self, typed_text, current_text):
        """Calculate suffix with intelligent duplicate line detection and skipping."""
        if not current_text:
            return "", 0, ""

        if not typed_text:
            return current_text, 0, ""

        # First check if current text starts with typed text (basic prefix match)
        if not current_text.startswith(typed_text):
            # Text was reset or changed - return entire current text
            return current_text, 0, ""

        # Detect duplicate lines
        duplicate_count, resume_position, skipped_content = self.detect_duplicate_lines(typed_text, current_text)

        if duplicate_count > 0:
            # We found duplicate lines - calculate suffix from resume position
            suffix = current_text[resume_position:] if resume_position < len(current_text) else ""

            print(f"Duplicate line detection: {duplicate_count} lines skipped")
            print(f"Skipped content: '{skipped_content[:50]}...'")
            print(f"Resume position: {resume_position}")
            print(f"Suffix to type: '{suffix[:50]}...'")

            # Update statistics
            self.duplicate_lines_skipped += duplicate_count
            self.total_lines_processed += duplicate_count
            self.last_duplicate_detection_time = time.time()

            return suffix, resume_position, skipped_content
        else:
            # No duplicates found - use standard append logic
            suffix = current_text[len(typed_text):]
            return suffix, len(typed_text), ""

    def create_temp_file(self):
        """Create a temporary file for storing session text."""
        try:
            # Create temp file with timestamp
            timestamp = int(time.time())
            temp_dir = tempfile.gettempdir()
            self.temp_file_path = os.path.join(temp_dir, f"autotyper_session_{timestamp}.txt")

            # Create empty file
            with open(self.temp_file_path, 'w', encoding='utf-8') as f:
                f.write("")

            self.temp_file_created = True
            print(f"Created temporary session file: {self.temp_file_path}")
            return True

        except Exception as e:
            print(f"Error creating temporary file: {e}")
            self.temp_file_path = None
            self.temp_file_created = False
            return False

    def save_capture_to_temp_file(self, text, original_text=None):
        """Save the current capture text to temporary file (replace contents).

        Args:
            text: The normalized text to save
            original_text: The original text before normalization (optional)
        """
        if not text:
            return False

        try:
            # Create temp file if it doesn't exist
            if not self.temp_file_created or not self.temp_file_path:
                if not self.create_temp_file():
                    return False

            # Store both normalized and original text with a separator
            content_to_save = text
            if original_text and original_text != text:
                # Add metadata section with original text
                content_to_save = f"{text}\n---ORIGINAL---\n{original_text}"

            # Replace entire file contents with new text
            with open(self.temp_file_path, 'w', encoding='utf-8') as f:
                f.write(content_to_save)

            print(f"Saved capture to temp file: {len(text)} characters")
            return True

        except Exception as e:
            print(f"Error saving to temporary file: {e}")
            return False

    def load_previous_capture(self):
        """Load the previous capture text from temporary file.

        Returns:
            tuple: (normalized_text, original_text) or just normalized_text if no original stored
        """
        if not self.temp_file_path or not os.path.exists(self.temp_file_path):
            return ""

        try:
            with open(self.temp_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check if file contains original text metadata
            if '\n---ORIGINAL---\n' in content:
                normalized_text, original_text = content.split('\n---ORIGINAL---\n', 1)
                print(f"Loaded previous capture: {len(normalized_text)} characters (with original)")
                return normalized_text, original_text
            else:
                print(f"Loaded previous capture: {len(content)} characters")
                return content

        except Exception as e:
            print(f"Error loading from temporary file: {e}")
            return ""

    def cleanup_temp_file(self):
        """Clean up the temporary file."""
        if self.temp_file_path and os.path.exists(self.temp_file_path):
            try:
                os.remove(self.temp_file_path)
                print(f"Cleaned up temporary file: {self.temp_file_path}")
            except Exception as e:
                print(f"Error cleaning up temporary file: {e}")

        self.temp_file_path = None
        self.temp_file_created = False

    def detect_cross_capture_duplicates(self, previous_text, current_text):
        """Detect overlapping content between previous capture and current capture.

        Returns:
            tuple: (duplicate_lines_count, resume_position, skipped_content)
        """
        if not previous_text or not current_text:
            return 0, 0, ""

        # Split both texts into lines
        previous_lines = previous_text.split('\n')
        current_lines = current_text.split('\n')

        # Look for overlap between the end of previous text and start of current text
        # We'll check the last N lines of previous text against the first N lines of current text
        max_overlap_lines = min(len(previous_lines), len(current_lines), 10)  # Limit to 10 lines for efficiency

        best_overlap = 0
        best_resume_position = 0
        best_skipped_content = ""

        # Try different overlap sizes, starting from the largest
        for overlap_size in range(max_overlap_lines, 0, -1):
            # Get the last N lines from previous text
            prev_tail_lines = previous_lines[-overlap_size:]
            # Get the first N lines from current text
            curr_head_lines = current_lines[:overlap_size]

            # Check if they match (using stricter criteria to reduce false positives)
            matches = 0
            for i in range(overlap_size):
                # Use very conservative whitespace handling - preserve all whitespace differences
                # Only strip newlines, but preserve spaces, tabs, etc.
                prev_line = prev_tail_lines[i].rstrip('\n\r')
                curr_line = curr_head_lines[i].rstrip('\n\r')

                # Must be EXACTLY identical (including whitespace) and contain multiple words
                # Added minimum length requirement to avoid matching very short lines
                if (prev_line == curr_line and
                    len(prev_line.split()) > 1 and
                    len(prev_line) >= 8):  # Increased minimum to 8 characters to avoid trivial matches
                    matches += 1
                else:
                    break  # Stop at first non-match

            # If we found a complete overlap, this is our best match
            if matches == overlap_size and matches > best_overlap:
                best_overlap = matches

                # Calculate resume position (start of non-overlapping content)
                best_resume_position = 0
                for i in range(matches):
                    best_resume_position += len(current_lines[i])
                    if i < matches - 1:  # Add newline after each line except the last
                        best_resume_position += 1

                # If there are more lines after the overlap, add newline after last overlapping line
                if matches < len(current_lines):
                    best_resume_position += 1

                # Build skipped content
                best_skipped_content = '\n'.join(curr_head_lines[:matches])

                break  # Found the best overlap, no need to check smaller ones

        return best_overlap, best_resume_position, best_skipped_content

    def detect_prefix_based_duplicates(self, previous_text, current_text):
        """
        Detect prefix-based duplicates for single-line or extended content.

        This handles cases where current_text starts with previous_text but has additional content,
        which the line-based duplicate detection misses.

        Returns:
            tuple: (duplicate_chars_count, resume_position, skipped_content)
        """
        if not previous_text or not current_text:
            return 0, 0, ""

        # Check if current text starts with previous text (prefix match)
        if current_text.startswith(previous_text):
            # Perfect prefix match - skip the entire previous text
            resume_position = len(previous_text)
            skipped_content = previous_text

            print(f"Perfect prefix match found:")
            print(f"  Previous: '{previous_text}'")
            print(f"  Current: '{current_text}'")
            print(f"  Resume position: {resume_position}")
            print(f"  Skipped content: '{skipped_content}'")

            return len(previous_text), resume_position, skipped_content

        # Check for partial prefix match with word boundaries
        # This handles cases where OCR might have slight variations
        prev_words = previous_text.split()
        curr_words = current_text.split()

        if not prev_words or not curr_words:
            return 0, 0, ""

        # Find the longest matching prefix at word level
        matching_words = 0
        for i in range(min(len(prev_words), len(curr_words))):
            if prev_words[i] == curr_words[i]:
                matching_words += 1
            else:
                break

        # Require at least 2 matching words to consider it a meaningful prefix
        # (reduced from 3 to handle shorter phrases better)
        if matching_words >= 2:
            # Calculate character position after the matching words
            matched_text = ' '.join(curr_words[:matching_words])
            resume_position = len(matched_text)

            # Add space after last matched word if there are more words
            if matching_words < len(curr_words):
                resume_position += 1  # Add space before next word

            print(f"Partial prefix match found:")
            print(f"  Matching words: {matching_words}")
            print(f"  Matched text: '{matched_text}'")
            print(f"  Resume position: {resume_position}")

            return matching_words, resume_position, matched_text

        return 0, 0, ""

    def detect_enhanced_cross_capture_duplicates(self, previous_text, current_text):
        """
        Enhanced duplicate detection that combines line-based and prefix-based approaches.

        This method first tries the existing line-based detection, and if that fails,
        falls back to prefix-based detection for single-line content.

        Returns:
            tuple: (duplicate_count, resume_position, skipped_content)
        """
        self.debug_log("=== ENTERING detect_enhanced_cross_capture_duplicates ===", "detect_enhanced_cross_capture_duplicates")

        if REAL_WORLD_DEBUGGING:
            performance_profiler.start_timing("detect_enhanced_cross_capture_duplicates")

        if not previous_text or not current_text:
            self.debug_log("Missing previous_text or current_text - returning no duplicates", "detect_enhanced_cross_capture_duplicates")
            if REAL_WORLD_DEBUGGING:
                performance_profiler.end_timing("detect_enhanced_cross_capture_duplicates")
            return 0, 0, ""

        self.debug_log(f"Previous text length: {len(previous_text)}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"Current text length: {len(current_text)}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"Previous text preview: '{previous_text[:100]}...'", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"Current text preview: '{current_text[:100]}...'", "detect_enhanced_cross_capture_duplicates")

        # First, try the existing line-based duplicate detection
        self.debug_log("Trying line-based duplicate detection...", "detect_enhanced_cross_capture_duplicates")
        line_duplicates, line_resume_pos, line_skipped = self.detect_cross_capture_duplicates(previous_text, current_text)

        self.debug_log(f"Line-based detection results:", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  duplicates: {line_duplicates}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  resume_pos: {line_resume_pos}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  skipped: '{line_skipped[:100]}...'", "detect_enhanced_cross_capture_duplicates")

        if line_duplicates > 0:
            self.debug_log(f"Line-based duplicate detection successful: {line_duplicates} lines", "detect_enhanced_cross_capture_duplicates")
            if REAL_WORLD_DEBUGGING:
                performance_profiler.end_timing("detect_enhanced_cross_capture_duplicates")
            return line_duplicates, line_resume_pos, line_skipped

        # If line-based detection failed, try prefix-based detection
        self.debug_log("Line-based detection failed, trying prefix-based detection...", "detect_enhanced_cross_capture_duplicates")
        prefix_duplicates, prefix_resume_pos, prefix_skipped = self.detect_prefix_based_duplicates(previous_text, current_text)

        self.debug_log(f"Prefix-based detection results:", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  duplicates: {prefix_duplicates}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  resume_pos: {prefix_resume_pos}", "detect_enhanced_cross_capture_duplicates")
        self.debug_log(f"  skipped: '{prefix_skipped[:100]}...'", "detect_enhanced_cross_capture_duplicates")

        if prefix_duplicates > 0:
            self.debug_log(f"Prefix-based duplicate detection successful: {prefix_duplicates} characters/words", "detect_enhanced_cross_capture_duplicates")
            return prefix_duplicates, prefix_resume_pos, prefix_skipped

        self.debug_log("No duplicates found with either method", "detect_enhanced_cross_capture_duplicates")

        if REAL_WORLD_DEBUGGING:
            performance_profiler.end_timing("detect_enhanced_cross_capture_duplicates")

        return 0, 0, ""

    def detect_partial_duplicates_and_get_suffix(self, current_text):
        """
        Detect partial duplicates using enhanced cross-capture detection and return the suffix to type.

        This method loads the previous capture and uses enhanced duplicate detection to find
        overlapping content, then returns only the new content that needs to be typed.

        Returns:
            tuple: (new_text_to_type, resume_position, skipped_content)
        """
        self.debug_log("=== ENTERING detect_partial_duplicates_and_get_suffix ===", "detect_partial_duplicates_and_get_suffix")

        if not current_text:
            self.debug_log("No current text - returning empty", "detect_partial_duplicates_and_get_suffix")
            return "", 0, ""

        self.debug_log(f"Current text length: {len(current_text)}", "detect_partial_duplicates_and_get_suffix")

        # Load previous capture content
        self.debug_log("Loading previous capture content", "detect_partial_duplicates_and_get_suffix")
        previous_capture_result = self.load_previous_capture()

        if not previous_capture_result:
            self.debug_log("No previous capture found for partial duplicate detection", "detect_partial_duplicates_and_get_suffix")
            return current_text, 0, ""  # Type all content as new

        # Handle both old format (string) and new format (tuple)
        if isinstance(previous_capture_result, tuple):
            previous_capture, _ = previous_capture_result  # Ignore previous_original for now
            self.debug_log(f"Previous capture loaded from tuple format (length: {len(previous_capture)})", "detect_partial_duplicates_and_get_suffix")
        else:
            previous_capture = previous_capture_result
            self.debug_log(f"Previous capture loaded from string format (length: {len(previous_capture)})", "detect_partial_duplicates_and_get_suffix")

        # Use enhanced cross-capture duplicate detection
        self.debug_log("Calling detect_enhanced_cross_capture_duplicates", "detect_partial_duplicates_and_get_suffix")
        duplicates, resume_pos, skipped_content = self.detect_enhanced_cross_capture_duplicates(previous_capture, current_text)

        self.debug_log(f"detect_enhanced_cross_capture_duplicates returned:", "detect_partial_duplicates_and_get_suffix")
        self.debug_log(f"  duplicates: {duplicates}", "detect_partial_duplicates_and_get_suffix")
        self.debug_log(f"  resume_pos: {resume_pos}", "detect_partial_duplicates_and_get_suffix")
        self.debug_log(f"  skipped_content: '{skipped_content[:100]}...'", "detect_partial_duplicates_and_get_suffix")

        if duplicates > 0 and resume_pos > 0:
            # Found duplicates - calculate the new text to type
            new_text = current_text[resume_pos:] if resume_pos < len(current_text) else ""

            self.debug_log(f"Partial duplicate detection results:", "detect_partial_duplicates_and_get_suffix")
            self.debug_log(f"  Duplicates found: {duplicates}", "detect_partial_duplicates_and_get_suffix")
            self.debug_log(f"  Resume position: {resume_pos}", "detect_partial_duplicates_and_get_suffix")
            self.debug_log(f"  Skipped content: '{skipped_content[:50]}...'", "detect_partial_duplicates_and_get_suffix")
            self.debug_log(f"  New text to type: '{new_text[:50]}...'", "detect_partial_duplicates_and_get_suffix")

            # Update statistics
            if hasattr(self, 'cross_capture_duplicates_skipped'):
                self.cross_capture_duplicates_skipped += duplicates
            if hasattr(self, 'total_lines_processed'):
                self.total_lines_processed += duplicates
            if hasattr(self, 'last_cross_capture_detection_time'):
                import time
                self.last_cross_capture_detection_time = time.time()

            return new_text, resume_pos, skipped_content
        else:
            self.debug_log("No partial duplicates found", "detect_partial_duplicates_and_get_suffix")
            return current_text, 0, ""  # Type all content as new

    def detect_content_overlap(self, typed_text, current_text):
        """Detect if there's meaningful overlap between typed text and current text.

        This helps distinguish between:
        - Content continuation (should type new parts)
        - Completely different content (should skip typing)

        Returns:
            bool: True if overlap detected and typing should continue, False otherwise
        """
        if not typed_text or not current_text:
            return False

        # Check for exact substring matches (either direction)
        if typed_text in current_text or current_text in typed_text:
            print(f"Exact substring overlap detected")
            return True

        # Check for significant word overlap
        # Filter out very common words AND common structural/technical terms that shouldn't count toward meaningful overlap
        common_stop_words = {
            # Basic stop words
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'this', 'that', 'these', 'those', 'it', 'its',
            'they', 'them', 'their', 'we', 'you', 'your', 'our', 'my', 'me', 'him', 'her', 'his',

            # Common structural terms that appear in many documents
            'chapter', 'section', 'part', 'introduction', 'conclusion', 'summary', 'overview',
            'tutorial', 'guide', 'manual', 'documentation', 'reference', 'example', 'examples',
            'step', 'steps', 'method', 'methods', 'approach', 'technique', 'techniques',
            'basic', 'advanced', 'beginner', 'beginners', 'expert', 'experts', 'intermediate',

            # Common technical terms that appear across different topics
            'programming', 'language', 'languages', 'code', 'coding', 'development', 'developer',
            'system', 'systems', 'application', 'applications', 'software', 'hardware',
            'data', 'information', 'content', 'text', 'file', 'files', 'document', 'documents'
        }

        typed_words = set(word for word in typed_text.lower().split() if len(word) > 2 and word not in common_stop_words)
        current_words = set(word for word in current_text.lower().split() if len(word) > 2 and word not in common_stop_words)

        if not typed_words or not current_words:
            return False

        # Calculate word overlap percentage
        common_words = typed_words.intersection(current_words)
        overlap_percentage = len(common_words) / min(len(typed_words), len(current_words))

        print(f"Word overlap: {len(common_words)} common words, {overlap_percentage:.2f} overlap percentage")
        print(f"Filtered typed words: {typed_words}")
        print(f"Filtered current words: {current_words}")
        print(f"Common words: {common_words}")

        # Require both significant overlap percentage AND minimum number of common words
        # Increased threshold to 70% and require at least 3 common meaningful words
        if overlap_percentage > 0.7 and len(common_words) >= 3:  # 70% word overlap threshold + minimum 3 words
            print(f"Significant word overlap detected ({overlap_percentage:.2f} with {len(common_words)} common words)")
            return True

        # Check for line-based overlap (useful for documents with line breaks)
        typed_lines = [line.strip() for line in typed_text.split('\n') if line.strip()]
        current_lines = [line.strip() for line in current_text.split('\n') if line.strip()]

        if typed_lines and current_lines:
            common_lines = set(typed_lines).intersection(set(current_lines))
            line_overlap_percentage = len(common_lines) / min(len(typed_lines), len(current_lines))

            print(f"Line overlap: {len(common_lines)} common lines, {line_overlap_percentage:.2f} overlap percentage")

            # Increased threshold to 70% and require at least 2 common lines to reduce false positives
            if line_overlap_percentage > 0.7 and len(common_lines) >= 2:  # 70% line overlap threshold + minimum 2 lines
                print(f"Significant line overlap detected ({line_overlap_percentage:.2f} with {len(common_lines)} common lines)")
                return True

        print("No significant overlap detected")
        return False

    def is_content_already_typed(self, current_text):
        """Check if the current text was already typed in a previous session.

        This method uses the cross-capture duplicate detection to determine if content
        should be skipped because it was already processed before.

        Returns:
            bool: True if content was already typed, False if it's new content
        """
        if not current_text:
            return False

        # Load previous capture content
        previous_capture_result = self.load_previous_capture()
        if not previous_capture_result:
            print("No previous capture found - treating as new content")
            return False

        # Handle both old format (string) and new format (tuple)
        if isinstance(previous_capture_result, tuple):
            previous_capture, previous_original = previous_capture_result
        else:
            previous_capture = previous_capture_result
            previous_original = None

        # Use the enhanced cross-capture duplicate detection logic
        duplicates, resume_pos, _ = self.detect_enhanced_cross_capture_duplicates(previous_capture, current_text)

        if duplicates > 0:
            # Calculate what percentage of the current text would be skipped
            skip_percentage = resume_pos / len(current_text) if current_text else 0

            print(f"Cross-capture analysis: {duplicates} duplicate lines, {skip_percentage:.2f} skip percentage")

            # Additional validation for high skip percentages to prevent false positives
            if skip_percentage > 0.9:
                # For very high skip percentages, do additional validation

                # Check 1: If texts are very short, require exact match
                if len(current_text) < 20:
                    print(f"Very short text ({len(current_text)} chars) - requiring exact match")
                    if previous_capture != current_text:  # Exact match required, no strip()
                        print(f"Short texts differ exactly - likely false positive")
                        return False

                # Check 2: Look for normalization-induced false positives
                # If we have the original text, compare it with current text to detect normalization issues
                if previous_original is not None:
                    # Compare original previous text with current text
                    if previous_original != current_text and previous_capture == current_text:
                        print(f"Normalization made different texts appear identical - likely false positive")
                        print(f"Original previous: '{previous_original}'")
                        print(f"Current: '{current_text}'")
                        print(f"Normalized previous: '{previous_capture}'")
                        return False

                # Fallback: Look for whitespace-only differences
                prev_normalized = ' '.join(previous_capture.split())  # Normalize all whitespace to single spaces
                curr_normalized = ' '.join(current_text.split())

                if prev_normalized == curr_normalized and previous_capture != current_text:
                    print(f"Texts differ only in whitespace - likely false positive from normalization")
                    return False

                # Check 3: Calculate character-level similarity for additional validation
                char_similarity = self.calculate_text_similarity(previous_capture, current_text)
                print(f"Character-level similarity: {char_similarity:.2f}")

                # If character similarity is low but skip percentage is high, it's likely a false positive
                if char_similarity < 0.85:  # Slightly higher threshold for better precision
                    print(f"Low character similarity ({char_similarity:.2f}) despite high skip percentage - likely false positive")
                    return False

                # Check 4: For single-line content, be extra conservative
                if '\n' not in current_text and len(current_text.split()) <= 3:
                    print(f"Single short line - requiring very high similarity")
                    if char_similarity < 0.95:
                        print(f"Insufficient similarity for short single line - likely false positive")
                        return False

                print(f"High skip percentage ({skip_percentage:.2f}) with good similarity - content likely already typed")
                return True
            else:
                print(f"Low skip percentage ({skip_percentage:.2f}) - content has new parts")
                return False
        else:
            print("No cross-capture duplicates found - content is new")
            return False

    def calculate_smart_append_suffix_with_file(self, current_text):
        """Enhanced suffix calculation that considers both typed text and file-based previous capture."""
        self.debug_log("=== ENTERING calculate_smart_append_suffix_with_file ===", "calculate_smart_append_suffix_with_file")

        if not current_text:
            self.debug_log("No current text - returning empty", "calculate_smart_append_suffix_with_file")
            return "", 0, ""

        self.debug_log(f"Current text length: {len(current_text)}", "calculate_smart_append_suffix_with_file")
        self.debug_log(f"Typed text length: {len(self.typed_text)}", "calculate_smart_append_suffix_with_file")

        # If no text has been typed yet, don't use cross-capture duplicate detection
        # This ensures initial typing always happens even if similar text was captured before
        if not self.typed_text:
            self.debug_log("No typed text yet - bypassing cross-capture duplicate detection for initial typing", "calculate_smart_append_suffix_with_file")
            result = self.calculate_smart_append_suffix(self.typed_text, current_text)
            self.debug_log(f"calculate_smart_append_suffix returned: {result}", "calculate_smart_append_suffix_with_file")
            return result

        # First, try cross-capture duplicate detection with file contents
        self.debug_log("Loading previous capture for cross-capture duplicate detection", "calculate_smart_append_suffix_with_file")
        previous_capture = self.load_previous_capture()
        cross_capture_duplicates = 0
        cross_capture_resume_pos = 0
        cross_capture_skipped = ""

        if previous_capture:
            self.debug_log(f"Previous capture found (length: {len(previous_capture)})", "calculate_smart_append_suffix_with_file")
            self.debug_log("Calling detect_enhanced_cross_capture_duplicates", "calculate_smart_append_suffix_with_file")

            cross_capture_duplicates, cross_capture_resume_pos, cross_capture_skipped = \
                self.detect_enhanced_cross_capture_duplicates(previous_capture, current_text)

            self.debug_log(f"detect_enhanced_cross_capture_duplicates returned:", "calculate_smart_append_suffix_with_file")
            self.debug_log(f"  duplicates: {cross_capture_duplicates}", "calculate_smart_append_suffix_with_file")
            self.debug_log(f"  resume_pos: {cross_capture_resume_pos}", "calculate_smart_append_suffix_with_file")
            self.debug_log(f"  skipped: '{cross_capture_skipped[:100]}...'", "calculate_smart_append_suffix_with_file")

            if cross_capture_duplicates > 0:
                # Found cross-capture duplicates
                suffix = current_text[cross_capture_resume_pos:] if cross_capture_resume_pos < len(current_text) else ""

                self.debug_log(f"Cross-capture duplicate detection: {cross_capture_duplicates} lines skipped", "calculate_smart_append_suffix_with_file")
                self.debug_log(f"Skipped content: '{cross_capture_skipped[:50]}...'", "calculate_smart_append_suffix_with_file")
                self.debug_log(f"Resume position: {cross_capture_resume_pos}", "calculate_smart_append_suffix_with_file")
                self.debug_log(f"Suffix to type: '{suffix[:50]}...'", "calculate_smart_append_suffix_with_file")

                # Update statistics
                self.cross_capture_duplicates_skipped += cross_capture_duplicates
                self.total_lines_processed += cross_capture_duplicates
                self.last_cross_capture_detection_time = time.time()

                return suffix, cross_capture_resume_pos, cross_capture_skipped
        else:
            self.debug_log("No previous capture found", "calculate_smart_append_suffix_with_file")

        # If no cross-capture duplicates found, fall back to regular duplicate detection
        self.debug_log("Falling back to regular duplicate detection", "calculate_smart_append_suffix_with_file")
        result = self.calculate_smart_append_suffix(self.typed_text, current_text)
        self.debug_log(f"calculate_smart_append_suffix returned: {result}", "calculate_smart_append_suffix_with_file")
        return result

    def random_delay(self):
        """Generate a random delay based on the configured delay and variance."""
        return self.delay + random.uniform(-self.variance, self.variance) * self.delay

    def validate_trailing_space_delay(self, value):
        """Validate and clamp the trailing space delay value to acceptable range."""
        try:
            delay = float(value)
            # Clamp to range [0.1, 10.0] seconds
            if delay < 0.1:
                print(f"Warning: Trailing space delay {delay} too small, setting to minimum 0.1 seconds")
                return 0.1
            elif delay > 10.0:
                print(f"Warning: Trailing space delay {delay} too large, setting to maximum 10.0 seconds")
                return 10.0
            else:
                return delay
        except ValueError:
            print(f"Error: Invalid trailing space delay value '{value}', using default 1.0 seconds")
            return 1.0

    def check_and_add_trailing_space(self):
        """Check if conditions are met to add a trailing space and add it if needed."""
        current_time = time.time()

        # Check if we have finished typing all detected text
        if self.typed_text != self.current_text:
            # Still typing, reset trailing space state
            self.trailing_space_added = False
            return

        # Check if we already added a trailing space for this detection cycle
        if self.trailing_space_added:
            return

        # Check if enough time has passed since typing completion
        if current_time - self.last_typing_completion_time < self.text_stability_threshold:
            return

        # Check if text has remained stable (no new text detected)
        if self.current_text != self.last_stable_text:
            # Text changed, update stability tracking
            self.last_stable_text = self.current_text
            self.text_stable_since = current_time
            return

        # Check if text has been stable for the required duration
        if current_time - self.text_stable_since < self.text_stability_threshold:
            return

        # Check if the detected text doesn't already end with whitespace
        if not self.current_text or self.current_text[-1] in ' \t\n':
            return

        # All conditions met - add trailing space
        print("Adding trailing space after stable text detection")

        # Position cursor at the end and add space
        self.position_cursor_at_end()

        with self.lock:
            pyautogui.typewrite(' ', interval=self.random_delay())

        # Update our tracking to include the space
        self.typed_text += ' '
        self.cursor_position += 1
        self.trailing_space_added = True

        print(f"Trailing space added. New typed text length: {len(self.typed_text)}")

    def start_typing(self):
        """Start the enhanced incremental typing process."""
        self.delay = float(self.delay_entry.get())
        self.variance = float(self.variance_entry.get()) / 100
        self.error_rate = float(self.error_rate_entry.get()) / 100

        # Read and validate trailing space delay from GUI
        self.text_stability_threshold = self.validate_trailing_space_delay(
            self.trailing_space_delay_entry.get()
        )

        if not hasattr(self, 'area'):
            print("Please select an area first!")
            return

        # Reset state for new typing session
        self.current_text = ""
        self.previous_text = ""
        self.typed_text = ""
        self.cursor_position = 0
        self.text_history.clear()

        # Reset trailing space state
        self.last_typing_completion_time = 0
        self.text_stable_since = 0
        self.trailing_space_added = False
        self.last_stable_text = ""

        # Reset duplicate detection state
        self.duplicate_lines_skipped = 0
        self.total_lines_processed = 0
        self.last_duplicate_detection_time = 0

        # Reset cursor removal state
        self.cursor_artifacts_removed = 0
        self.last_cursor_removal_time = 0

        # Reset cross-capture duplicate detection state
        self.cross_capture_duplicates_skipped = 0
        self.last_cross_capture_detection_time = 0

        # Countdown
        countdown = int(self.countdown_entry.get())
        for i in range(countdown, 0, -1):
            print(f"Starting in {i}...")
            time.sleep(1)

        # Start the typing process
        if REAL_WORLD_DEBUGGING:
            thread_monitor.log_typing_active_change(self.typing_active, True, "start_typing()", threading.current_thread().name)
            thread_monitor.log_capture_active_change(self.capture_active, True, "start_typing()", threading.current_thread().name)

        self.typing_active = True
        self.capture_active = True

        # Reset state for fresh start
        self.current_text = ""
        self.previous_text = ""
        self.typed_text = ""
        self.cursor_position = 0

        x1, y1, x2, y2 = self.area

        print(f"Starting typing with area: {self.area}")
        print(f"Initial state - Current: '{self.current_text}', Previous: '{self.previous_text}', Typed: '{self.typed_text}'")

        # Start capture and typing threads
        capture_thread = threading.Thread(target=self.enhanced_capture_text, args=((x1, y1, x2, y2),))
        typing_thread = threading.Thread(target=self.enhanced_typing_loop)

        capture_thread.daemon = True
        typing_thread.daemon = True

        capture_thread.start()
        typing_thread.start()

        print("Enhanced incremental typing started!")

    def pause_typing(self):
        """Pause the typing process."""
        if REAL_WORLD_DEBUGGING:
            thread_monitor.log_typing_active_change(self.typing_active, False, "pause_typing()", threading.current_thread().name)
            thread_monitor.log_capture_active_change(self.capture_active, False, "pause_typing()", threading.current_thread().name)

        self.typing_active = False
        self.capture_active = False
        print("Typing paused.")

    def enhanced_capture_text(self, bbox):
        """Enhanced text capture with stability checking."""
        if REAL_WORLD_DEBUGGING:
            thread_monitor.log_thread_event("CAPTURE_THREAD_STARTED", threading.current_thread().name, f"bbox={bbox}")

        while self.capture_active:
            try:
                if REAL_WORLD_DEBUGGING:
                    performance_profiler.start_timing("ocr_capture")

                # Capture image and extract text
                img = ImageGrab.grab(bbox=bbox)
                raw_text = pytesseract.image_to_string(img).strip()

                if REAL_WORLD_DEBUGGING:
                    performance_profiler.end_timing("ocr_capture")
                    performance_profiler.start_timing("text_processing")

                # Apply cursor artifact removal if enabled
                if self.cursor_removal_enabled and raw_text:
                    raw_text = self.remove_cursor_artifacts(raw_text)

                # Get stable text using history
                stable_text = self.get_stable_text(raw_text)

                # Update current text if it's significantly different
                similarity = self.calculate_text_similarity(self.current_text, stable_text)

                if REAL_WORLD_DEBUGGING:
                    performance_profiler.end_timing("text_processing")
                    # Log OCR capture for analysis
                    ocr_analyzer.log_ocr_capture(raw_text, stable_text, similarity)

                if similarity < 0.9:  # Text has changed significantly
                    if REAL_WORLD_DEBUGGING:
                        thread_monitor.log_thread_event("TEXT_CHANGE_DETECTED", threading.current_thread().name,
                                                       f"similarity={similarity:.2f}, length={len(stable_text)}")

                    self.previous_text = self.current_text
                    self.current_text = stable_text

                    # Save the cleaned text to temporary file for cross-capture duplicate detection
                    self.save_capture_to_temp_file(stable_text, raw_text)

                    # Update text preview
                    self.text_preview.delete(1.0, tk.END)
                    self.text_preview.insert(tk.END, stable_text)

                    print(f"Text updated: '{stable_text[:50]}...' (similarity: {similarity:.2f})")
                    print(f"Current text length: {len(self.current_text)}, Typed text length: {len(self.typed_text)}")

                time.sleep(0.5)  # Capture every 500ms for responsiveness

            except Exception as e:
                if REAL_WORLD_DEBUGGING:
                    thread_monitor.log_thread_event("CAPTURE_ERROR", threading.current_thread().name, str(e))
                print(f"Error in text capture: {e}")
                time.sleep(1)

        if REAL_WORLD_DEBUGGING:
            thread_monitor.log_thread_event("CAPTURE_THREAD_ENDED", threading.current_thread().name)

    def enhanced_typing_loop(self):
        """Enhanced typing loop with incremental updates and trailing space feature."""
        self.debug_log("Enhanced typing loop started", "enhanced_typing_loop")

        while self.typing_active:
            try:
                self.debug_log(f"Loop iteration - typing_active: {self.typing_active}", "enhanced_typing_loop")

                # Check if text has changed OR if we have text but haven't typed anything yet
                text_changed = self.current_text != self.previous_text
                has_untyped_text = self.current_text and not self.typed_text

                self.debug_log(f"Text changed: {text_changed}, Has untyped text: {has_untyped_text}", "enhanced_typing_loop")
                self.debug_log(f"Current text length: {len(self.current_text)}, Typed text length: {len(self.typed_text)}", "enhanced_typing_loop")

                if text_changed or has_untyped_text:
                    self.debug_log("Calling process_text_changes()", "enhanced_typing_loop")
                    self.process_text_changes()
                    self.debug_log("process_text_changes() completed", "enhanced_typing_loop")

                # Check if we should add a trailing space
                self.check_and_add_trailing_space()

                time.sleep(0.1)  # Check for changes frequently

            except Exception as e:
                self.debug_log(f"ERROR in typing loop: {e}", "enhanced_typing_loop")
                import traceback
                traceback.print_exc()
                time.sleep(1)

        self.debug_log("Enhanced typing loop ended", "enhanced_typing_loop")

    def process_text_changes(self):
        """Process changes using append-only logic with intelligent duplicate line detection."""
        self.debug_log("=== ENTERING process_text_changes ===", "process_text_changes")

        if not self.current_text:
            self.debug_log("No current text to process - returning", "process_text_changes")
            return

        self.debug_log(f"Current text: '{self.current_text[:100]}...' (length: {len(self.current_text)})", "process_text_changes")
        self.debug_log(f"Typed text: '{self.typed_text[:100]}...' (length: {len(self.typed_text)})", "process_text_changes")
        self.debug_log(f"typing_active: {self.typing_active}", "process_text_changes")

        # Check if this is initial typing (no text typed yet)
        if not self.typed_text:
            self.debug_log("=== INITIAL TYPING PATH ===", "process_text_changes")
            self.debug_log(f"Starting fresh with: '{self.current_text[:100]}...'", "process_text_changes")

            self.typed_text = ""
            self.cursor_position = 0

            # Position cursor at the beginning and type all current text
            self.debug_log("Positioning cursor at end for initial typing", "process_text_changes")
            self.position_cursor_at_end()

            if self.current_text:
                self.debug_log(f"About to type initial text: '{self.current_text[:100]}...'", "process_text_changes")
                self.debug_log(f"typing_active before type_text_with_effects: {self.typing_active}", "process_text_changes")

                self.type_text_with_effects(self.current_text)

                self.debug_log(f"typing_active after type_text_with_effects: {self.typing_active}", "process_text_changes")
                self.typed_text = self.current_text
                self.cursor_position = len(self.current_text)

                # Mark typing completion time for trailing space feature
                self.last_typing_completion_time = time.time()
                self.trailing_space_added = False  # Reset for new text
                self.debug_log("Initial typing completed successfully", "process_text_changes")
        elif self.current_text.startswith(self.typed_text):
            # Current text extends previously typed text - use incremental typing
            self.debug_log("=== INCREMENTAL TYPING PATH ===", "process_text_changes")
            self.debug_log("Current text extends typed text", "process_text_changes")

            self.debug_log("Calling calculate_smart_append_suffix_with_file()", "process_text_changes")
            new_text, resume_position, skipped_content = self.calculate_smart_append_suffix_with_file(self.current_text)
            self.debug_log(f"calculate_smart_append_suffix_with_file() returned:", "process_text_changes")
            self.debug_log(f"  new_text: '{new_text[:100]}...' (length: {len(new_text)})", "process_text_changes")
            self.debug_log(f"  resume_position: {resume_position}", "process_text_changes")
            self.debug_log(f"  skipped_content: '{skipped_content[:100]}...' (length: {len(skipped_content)})", "process_text_changes")

            if new_text:
                self.debug_log("New text found - proceeding with typing", "process_text_changes")

                if skipped_content:
                    # We skipped duplicate lines - update typed_text to include skipped content
                    # and position cursor at resume point
                    self.debug_log(f"Skipping duplicate lines and appending new text: '{new_text[:50]}...'", "process_text_changes")

                    # Update typed_text to include content up to resume position
                    old_typed_text = self.typed_text
                    self.typed_text = self.current_text[:resume_position]
                    self.cursor_position = resume_position

                    self.debug_log(f"Updated typed_text from length {len(old_typed_text)} to {len(self.typed_text)}", "process_text_changes")
                    self.debug_log(f"Updated cursor_position to {self.cursor_position}", "process_text_changes")

                    # Position cursor at resume point
                    self.debug_log("Positioning cursor at resume point", "process_text_changes")
                    self.position_cursor_at(resume_position)
                else:
                    self.debug_log(f"Appending new text: '{new_text[:50]}...'", "process_text_changes")

                    # Ensure cursor is at the end of previously typed text
                    self.debug_log("Positioning cursor at end", "process_text_changes")
                    self.position_cursor_at_end()

                # Type only the new text
                self.debug_log(f"About to type new text: '{new_text[:100]}...'", "process_text_changes")
                self.debug_log(f"typing_active before typing: {self.typing_active}", "process_text_changes")

                self.type_text_with_effects(new_text)

                self.debug_log(f"typing_active after typing: {self.typing_active}", "process_text_changes")

                # Update our tracking
                self.typed_text = self.current_text
                self.cursor_position = len(self.current_text)

                # Mark typing completion time for trailing space feature
                self.last_typing_completion_time = time.time()
                self.trailing_space_added = False  # Reset for new text
                self.debug_log("Incremental typing completed successfully", "process_text_changes")
            else:
                self.debug_log("No new text to append", "process_text_changes")
        else:
            # Current text doesn't start with typed text - this is a content change
            # This could be due to scrolling, cursor movement, or switching to different content
            self.debug_log("=== CONTENT CHANGE DETECTION PATH ===", "process_text_changes")
            self.debug_log("Current text doesn't start with typed text", "process_text_changes")
            self.debug_log(f"Typed text: '{self.typed_text[:100]}...'", "process_text_changes")
            self.debug_log(f"Current text: '{self.current_text[:100]}...'", "process_text_changes")

            # First, try to detect partial duplicates using enhanced cross-capture detection
            self.debug_log("Checking for partial duplicates with enhanced cross-capture detection...", "process_text_changes")
            new_text, resume_position, skipped_content = self.detect_partial_duplicates_and_get_suffix(self.current_text)

            self.debug_log(f"detect_partial_duplicates_and_get_suffix() returned:", "process_text_changes")
            self.debug_log(f"  new_text: '{new_text[:100]}...' (length: {len(new_text)})", "process_text_changes")
            self.debug_log(f"  resume_position: {resume_position}", "process_text_changes")
            self.debug_log(f"  skipped_content: '{skipped_content[:100]}...' (length: {len(skipped_content)})", "process_text_changes")

            if skipped_content:
                # Found partial duplicates - skip the duplicate portion and type only new content
                self.debug_log(f"Partial duplicates detected - skipping '{skipped_content[:50]}...' and typing new content", "process_text_changes")
                self.debug_log(f"Resume position: {resume_position}", "process_text_changes")
                self.debug_log(f"New text to type: '{new_text[:50]}...'", "process_text_changes")

                if new_text:
                    self.debug_log(f"🎯 NEW TEXT FOUND - preparing to type: '{new_text}'", "process_text_changes")
                    self.debug_log(f"   Length: {len(new_text)} characters", "process_text_changes")
                    self.debug_log(f"   Resume position: {resume_position}", "process_text_changes")
                    self.debug_log(f"   Current typing_active: {self.typing_active}", "process_text_changes")

                    # Update typed_text to include content up to resume position
                    old_typed_text = self.typed_text
                    self.typed_text = self.current_text[:resume_position]
                    self.cursor_position = resume_position

                    self.debug_log(f"   Updated typed_text from '{old_typed_text}' to '{self.typed_text}'", "process_text_changes")
                    self.debug_log(f"   Updated cursor_position to {self.cursor_position}", "process_text_changes")

                    # Position cursor at resume point and type new content
                    self.debug_log(f"   📍 Positioning cursor and typing new content...", "process_text_changes")
                    self.position_cursor_at(resume_position)

                    self.debug_log(f"   ⌨️  About to call type_text_with_effects...", "process_text_changes")
                    self.debug_log(f"   typing_active before type_text_with_effects: {self.typing_active}", "process_text_changes")

                    self.type_text_with_effects(new_text)

                    self.debug_log(f"   ✅ type_text_with_effects call completed", "process_text_changes")
                    self.debug_log(f"   typing_active after type_text_with_effects: {self.typing_active}", "process_text_changes")

                    # Update our tracking
                    self.typed_text = self.current_text
                    self.cursor_position = len(self.current_text)
                    self.last_typing_completion_time = time.time()
                    self.trailing_space_added = False

                    self.debug_log(f"   📊 Final state:", "process_text_changes")
                    self.debug_log(f"     typed_text: '{self.typed_text}'", "process_text_changes")
                    self.debug_log(f"     cursor_position: {self.cursor_position}", "process_text_changes")
                    self.debug_log(f"     typing_active: {self.typing_active}", "process_text_changes")
                else:
                    self.debug_log("❌ No new content to type after skipping duplicates - new_text is empty!", "process_text_changes")
            else:
                # No partial duplicates found - check if content was completely already typed
                already_typed = self.is_content_already_typed(self.current_text)

                if already_typed:
                    print("Content already typed in previous session - skipping to prevent retyping")
                else:
                    # This is genuinely new content - check for overlap with current typed text
                    overlap_detected = self.detect_content_overlap(self.typed_text, self.current_text)

                    if overlap_detected:
                        print("Overlap detected with current typed text - handling as continuation")
                        # Handle as incremental typing from the overlap point
                        new_text, resume_position, skipped_content = self.calculate_smart_append_suffix_with_file(self.current_text)

                        if new_text:
                            print(f"Typing continuation text: '{new_text[:50]}...'")
                            self.position_cursor_at_end()
                            self.type_text_with_effects(new_text)
                            self.typed_text = self.current_text
                            self.cursor_position = len(self.current_text)
                            self.last_typing_completion_time = time.time()
                            self.trailing_space_added = False
                    else:
                        # No overlap - type it as completely fresh content
                        print("No overlap detected - typing as fresh content")
                        self.position_cursor_at_end()
                        self.type_text_with_effects(self.current_text)
                        self.typed_text = self.current_text
                        self.cursor_position = len(self.current_text)
                        self.last_typing_completion_time = time.time()
                        self.trailing_space_added = False

        # Update previous text for next comparison
        self.previous_text = self.current_text

        self.debug_log("=== EXITING process_text_changes ===", "process_text_changes")
        self.debug_log(f"Final typing_active state: {self.typing_active}", "process_text_changes")

    def position_cursor_at_end(self):
        """Position cursor at the end of the currently typed text."""
        target_position = len(self.typed_text)

        if target_position == self.cursor_position:
            return

        print(f"Positioning cursor at end (position {target_position})")

        # Calculate movement needed
        movement = target_position - self.cursor_position

        with self.lock:
            if movement > 0:
                # Move cursor forward to end
                for _ in range(movement):
                    pyautogui.press('right')
                    time.sleep(0.01)
            else:
                # Move cursor backward to end position
                for _ in range(abs(movement)):
                    pyautogui.press('left')
                    time.sleep(0.01)

        self.cursor_position = target_position

    def position_cursor_at(self, target_position):
        """Position cursor at the specified text position."""
        print(f"📍 position_cursor_at called: {self.cursor_position} → {target_position}")

        if target_position == self.cursor_position:
            print(f"   ✅ Already at target position")
            return

        # Calculate movement needed
        movement = target_position - self.cursor_position
        print(f"   🔄 Moving cursor by {movement} positions")

        try:
            with self.lock:
                if movement > 0:
                    # Move cursor forward
                    print(f"   ➡️  Moving cursor forward {movement} positions")
                    for i in range(movement):
                        pyautogui.press('right')
                        time.sleep(0.01)
                        if i % 10 == 0:  # Log every 10 moves for long movements
                            print(f"     Moved {i+1}/{movement} positions")
                else:
                    # Move cursor backward
                    print(f"   ⬅️  Moving cursor backward {abs(movement)} positions")
                    for i in range(abs(movement)):
                        pyautogui.press('left')
                        time.sleep(0.01)
                        if i % 10 == 0:  # Log every 10 moves for long movements
                            print(f"     Moved {i+1}/{abs(movement)} positions")

            self.cursor_position = target_position
            print(f"   ✅ Cursor positioned at {self.cursor_position}")

        except Exception as e:
            print(f"   ❌ ERROR during cursor positioning: {e}")
            import traceback
            traceback.print_exc()

    def type_text_with_effects(self, text):
        """Type text with realistic effects (delays, errors, corrections)."""
        self.debug_log(f"🎯 type_text_with_effects called with text: '{text}' (length: {len(text)})", "type_text_with_effects")
        self.debug_log(f"   typing_active: {self.typing_active}", "type_text_with_effects")
        self.debug_log(f"   Current thread: {threading.current_thread().name}", "type_text_with_effects")

        if not text:
            self.debug_log(f"   ⚠️  WARNING: Empty text provided to type_text_with_effects!", "type_text_with_effects")
            return

        if not self.typing_active:
            self.debug_log(f"   ❌ typing_active is False - aborting typing!", "type_text_with_effects")
            return

        self.debug_log(f"   ✅ Starting to type {len(text)} characters...", "type_text_with_effects")

        try:
            for i, char in enumerate(text):
                if not self.typing_active:
                    self.debug_log(f"   ❌ typing_active became False at character {i} - breaking!", "type_text_with_effects")
                    break

                # Log every 10th character to avoid spam, but always log first and last few
                if i < 5 or i >= len(text) - 5 or i % 10 == 0:
                    self.debug_log(f"     [{i+1}/{len(text)}] Typing character: '{char}'", "type_text_with_effects")

                # Handle newlines
                if char == '\n':
                    print(f"       Pressing Enter for newline")
                    with self.lock:
                        pyautogui.press('enter')
                        time.sleep(self.random_delay())
                    continue

                # Simulate typing errors
                if random.random() < self.error_rate:
                    # Type a wrong character first
                    typo = random.choice('abcdefghijklmnopqrstuvwxyz')
                    print(f"       Simulating typo: '{typo}' then correcting to '{char}'")
                    try:
                        with self.lock:
                            # Add safety checks before typing typo
                            print(f"         🔒 Acquired lock for typo simulation")

                            if not self.typing_active:
                                print(f"         ❌ typing_active became False - aborting typo!")
                                break

                            # Type typo
                            print(f"         ⌨️  Typing typo: '{typo}'")
                            pyautogui.typewrite(typo, interval=self.random_delay())
                            time.sleep(0.1)  # Brief pause before correction

                            # Correct typo
                            print(f"         ⌫ Pressing backspace to correct typo")
                            pyautogui.press('backspace')
                            time.sleep(0.05)

                            # Type correct character
                            print(f"         ✅ Typing correct character: '{char}'")
                            pyautogui.typewrite(char, interval=self.random_delay())
                            print(f"         ✅ Typo correction completed for '{char}'")

                    except Exception as typo_error:
                        print(f"         ❌ ERROR during typo simulation for '{char}': {typo_error}")
                        # Try to type the correct character directly
                        try:
                            with self.lock:
                                pyautogui.typewrite(char, interval=self.random_delay())
                                print(f"         ✅ Fallback typing successful for '{char}'")
                        except Exception as fallback_error:
                            print(f"         ❌ Fallback typing also failed for '{char}': {fallback_error}")
                            continue
                else:
                    # Type the correct character
                    print(f"       Typing correct character: '{char}'")
                    try:
                        with self.lock:
                            # Add safety checks before typing
                            print(f"         🔒 Acquired lock for character '{char}'")

                            # Check if we're still active
                            if not self.typing_active:
                                print(f"         ❌ typing_active became False - aborting!")
                                break

                            # Get current mouse position for debugging
                            mouse_x, mouse_y = pyautogui.position()
                            print(f"         📍 Mouse position: ({mouse_x}, {mouse_y})")

                            # Type the character
                            delay = self.random_delay()
                            print(f"         ⏱️  Using delay: {delay:.3f}s")
                            pyautogui.typewrite(char, interval=delay)
                            print(f"         ✅ pyautogui.typewrite completed for '{char}'")

                    except Exception as char_error:
                        print(f"         ❌ ERROR typing character '{char}': {char_error}")
                        # Continue with next character instead of failing completely
                        continue

                if i < 5 or i >= len(text) - 5 or i % 10 == 0:
                    self.debug_log(f"       ✅ Character '{char}' typed successfully", "type_text_with_effects")

            self.debug_log(f"   ✅ Finished typing all {len(text)} characters successfully", "type_text_with_effects")
            self.debug_log(f"   Final typing_active state: {self.typing_active}", "type_text_with_effects")

        except Exception as e:
            self.debug_log(f"   ❌ ERROR during typing: {e}", "type_text_with_effects")
            import traceback
            traceback.print_exc()

    def type_text(self, text):
        """Legacy method - redirects to new implementation."""
        self.type_text_with_effects(text)

    def show_status(self):
        """Display current status and statistics."""
        # Calculate what would be appended next
        next_suffix = self.calculate_append_suffix(self.typed_text, self.current_text)
        is_prefix_match = self.current_text.startswith(self.typed_text) if self.typed_text else True

        # Calculate trailing space status
        current_time = time.time()
        time_since_completion = current_time - self.last_typing_completion_time if self.last_typing_completion_time > 0 else 0
        time_since_stable = current_time - self.text_stable_since if self.text_stable_since > 0 else 0
        text_ends_with_whitespace = self.current_text and self.current_text[-1] in ' \t\n'

        status_info = f"""
=== Append-Only Autotyper Status ===
Mode: APPEND-ONLY (No deletions/replacements)
Typing Active: {self.typing_active}
Capture Active: {self.capture_active}

Text Analysis:
- Current Text Length: {len(self.current_text)}
- Typed Text Length: {len(self.typed_text)}
- Cursor Position: {self.cursor_position}
- Text History Size: {len(self.text_history)}
- Prefix Match: {is_prefix_match}
- Next Suffix Length: {len(next_suffix)}

Trailing Space Feature:
- Trailing Space Added: {self.trailing_space_added}
- Time Since Typing Completion: {time_since_completion:.1f}s
- Time Since Text Stable: {time_since_stable:.1f}s
- Text Ends With Whitespace: {text_ends_with_whitespace}
- Stability Threshold: {self.text_stability_threshold}s (user-configurable: 0.1-10.0s)

Duplicate Line Detection:
- Within-Capture Duplicates Skipped: {self.duplicate_lines_skipped}
- Cross-Capture Duplicates Skipped: {self.cross_capture_duplicates_skipped}
- Total Lines Processed: {self.total_lines_processed}
- Overall Efficiency: {((self.duplicate_lines_skipped + self.cross_capture_duplicates_skipped) / max(self.total_lines_processed, 1) * 100):.1f}% lines skipped
- Last Within-Capture Detection: {time.time() - self.last_duplicate_detection_time:.1f}s ago (if detected)
- Last Cross-Capture Detection: {time.time() - self.last_cross_capture_detection_time:.1f}s ago (if detected)

Cursor Artifact Removal:
- Cursor Artifacts Removed: {self.cursor_artifacts_removed}
- Cursor Removal Enabled: {self.cursor_removal_enabled}
- Last Removal: {time.time() - self.last_cursor_removal_time:.1f}s ago (if removed)

Persistent Text Tracking:
- Temporary File Created: {self.temp_file_created}
- Temporary File Path: {self.temp_file_path or 'None'}
- Cross-Capture Detection Enabled: {self.temp_file_created}

Text Previews:
- Current Text: '{self.current_text[:100]}...'
- Typed Text: '{self.typed_text[:100]}...'
- Next Suffix: '{next_suffix[:50]}...'

Settings:
- Delay: {self.delay}s
- Variance: {self.variance * 100}%
- Error Rate: {self.error_rate * 100}%
"""
        print(status_info)

    def generate_debugging_report(self):
        """Generate comprehensive debugging report for real-world issues."""
        if not REAL_WORLD_DEBUGGING:
            print("Real-world debugging tools not available")
            return

        print("\n" + "="*80)
        print("COMPREHENSIVE REAL-WORLD DEBUGGING REPORT")
        print("="*80)

        # Generate individual reports
        thread_monitor.detect_race_conditions()
        ocr_analyzer.generate_ocr_report()
        performance_profiler.generate_performance_report()

        # Save debug session
        filename = save_debug_session()
        print(f"\nComplete debug session saved to: {filename}")

        return filename

    def on_window_close(self):
        """Handle window close event with proper cleanup."""
        # Generate debugging report if real-world debugging is enabled
        if REAL_WORLD_DEBUGGING:
            try:
                self.generate_debugging_report()
            except Exception as e:
                print(f"Error generating debugging report: {e}")

        self.exit_program()

    def exit_program(self):
        """Stop typing and close the application."""
        self.typing_active = False
        self.capture_active = False
        print("Shutting down autotyper...")

        # Clean up temporary file
        self.cleanup_temp_file()

        self.root.quit()

if __name__ == "__main__":
    root = tk.Tk()
    app = ScreenTextTyper(root)
    root.mainloop()
